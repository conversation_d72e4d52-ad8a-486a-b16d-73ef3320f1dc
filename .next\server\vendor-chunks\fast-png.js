"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-png";
exports.ids = ["vendor-chunks/fast-png"];
exports.modules = {

/***/ "(ssr)/./node_modules/fast-png/lib-esm/PngDecoder.js":
/*!*****************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/PngDecoder.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PngDecoder)\n/* harmony export */ });\n/* harmony import */ var iobuffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! iobuffer */ \"(ssr)/./node_modules/iobuffer/lib-esm/IOBuffer.js\");\n/* harmony import */ var pako__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pako */ \"(ssr)/./node_modules/pako/dist/pako.esm.mjs\");\n/* harmony import */ var _helpers_crc__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers/crc */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/crc.js\");\n/* harmony import */ var _helpers_decodeInterlaceAdam7__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./helpers/decodeInterlaceAdam7 */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/decodeInterlaceAdam7.js\");\n/* harmony import */ var _helpers_decodeInterlaceNull__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./helpers/decodeInterlaceNull */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/decodeInterlaceNull.js\");\n/* harmony import */ var _helpers_signature__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./helpers/signature */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/signature.js\");\n/* harmony import */ var _helpers_text__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./helpers/text */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/text.js\");\n/* harmony import */ var _internalTypes__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./internalTypes */ \"(ssr)/./node_modules/fast-png/lib-esm/internalTypes.js\");\n\n\n\n\n\n\n\n\nclass PngDecoder extends iobuffer__WEBPACK_IMPORTED_MODULE_0__.IOBuffer {\n    _checkCrc;\n    _inflator;\n    _png;\n    _apng;\n    _end;\n    _hasPalette;\n    _palette;\n    _hasTransparency;\n    _transparency;\n    _compressionMethod;\n    _filterMethod;\n    _interlaceMethod;\n    _colorType;\n    _isAnimated;\n    _numberOfFrames;\n    _numberOfPlays;\n    _frames;\n    _writingDataChunks;\n    constructor(data, options = {}) {\n        super(data);\n        const { checkCrc = false } = options;\n        this._checkCrc = checkCrc;\n        this._inflator = new pako__WEBPACK_IMPORTED_MODULE_1__.Inflate();\n        this._png = {\n            width: -1,\n            height: -1,\n            channels: -1,\n            data: new Uint8Array(0),\n            depth: 1,\n            text: {},\n        };\n        this._apng = {\n            width: -1,\n            height: -1,\n            channels: -1,\n            depth: 1,\n            numberOfFrames: 1,\n            numberOfPlays: 0,\n            text: {},\n            frames: [],\n        };\n        this._end = false;\n        this._hasPalette = false;\n        this._palette = [];\n        this._hasTransparency = false;\n        this._transparency = new Uint16Array(0);\n        this._compressionMethod = _internalTypes__WEBPACK_IMPORTED_MODULE_7__.CompressionMethod.UNKNOWN;\n        this._filterMethod = _internalTypes__WEBPACK_IMPORTED_MODULE_7__.FilterMethod.UNKNOWN;\n        this._interlaceMethod = _internalTypes__WEBPACK_IMPORTED_MODULE_7__.InterlaceMethod.UNKNOWN;\n        this._colorType = _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.UNKNOWN;\n        this._isAnimated = false;\n        this._numberOfFrames = 1;\n        this._numberOfPlays = 0;\n        this._frames = [];\n        this._writingDataChunks = false;\n        // PNG is always big endian\n        // https://www.w3.org/TR/PNG/#7Integers-and-byte-order\n        this.setBigEndian();\n    }\n    decode() {\n        (0,_helpers_signature__WEBPACK_IMPORTED_MODULE_5__.checkSignature)(this);\n        while (!this._end) {\n            const length = this.readUint32();\n            const type = this.readChars(4);\n            this.decodeChunk(length, type);\n        }\n        this.decodeImage();\n        return this._png;\n    }\n    decodeApng() {\n        (0,_helpers_signature__WEBPACK_IMPORTED_MODULE_5__.checkSignature)(this);\n        while (!this._end) {\n            const length = this.readUint32();\n            const type = this.readChars(4);\n            this.decodeApngChunk(length, type);\n        }\n        this.decodeApngImage();\n        return this._apng;\n    }\n    // https://www.w3.org/TR/PNG/#5Chunk-layout\n    decodeChunk(length, type) {\n        const offset = this.offset;\n        switch (type) {\n            // 11.2 Critical chunks\n            case 'IHDR': // 11.2.2 IHDR Image header\n                this.decodeIHDR();\n                break;\n            case 'PLTE': // 11.2.3 PLTE Palette\n                this.decodePLTE(length);\n                break;\n            case 'IDAT': // 11.2.4 IDAT Image data\n                this.decodeIDAT(length);\n                break;\n            case 'IEND': // 11.2.5 IEND Image trailer\n                this._end = true;\n                break;\n            // 11.3 Ancillary chunks\n            case 'tRNS': // 11.3.2.1 tRNS Transparency\n                this.decodetRNS(length);\n                break;\n            case 'iCCP': // ******** iCCP Embedded ICC profile\n                this.decodeiCCP(length);\n                break;\n            case _helpers_text__WEBPACK_IMPORTED_MODULE_6__.textChunkName: // ******** tEXt Textual data\n                (0,_helpers_text__WEBPACK_IMPORTED_MODULE_6__.decodetEXt)(this._png.text, this, length);\n                break;\n            case 'pHYs': // ******** pHYs Physical pixel dimensions\n                this.decodepHYs();\n                break;\n            default:\n                this.skip(length);\n                break;\n        }\n        if (this.offset - offset !== length) {\n            throw new Error(`Length mismatch while decoding chunk ${type}`);\n        }\n        if (this._checkCrc) {\n            (0,_helpers_crc__WEBPACK_IMPORTED_MODULE_2__.checkCrc)(this, length + 4, type);\n        }\n        else {\n            this.skip(4);\n        }\n    }\n    decodeApngChunk(length, type) {\n        const offset = this.offset;\n        if (type !== 'fdAT' && type !== 'IDAT' && this._writingDataChunks) {\n            this.pushDataToFrame();\n        }\n        switch (type) {\n            case 'acTL':\n                this.decodeACTL();\n                break;\n            case 'fcTL':\n                this.decodeFCTL();\n                break;\n            case 'fdAT':\n                this.decodeFDAT(length);\n                break;\n            default:\n                this.decodeChunk(length, type);\n                this.offset = offset + length;\n                break;\n        }\n        if (this.offset - offset !== length) {\n            throw new Error(`Length mismatch while decoding chunk ${type}`);\n        }\n        if (this._checkCrc) {\n            (0,_helpers_crc__WEBPACK_IMPORTED_MODULE_2__.checkCrc)(this, length + 4, type);\n        }\n        else {\n            this.skip(4);\n        }\n    }\n    // https://www.w3.org/TR/PNG/#11IHDR\n    decodeIHDR() {\n        const image = this._png;\n        image.width = this.readUint32();\n        image.height = this.readUint32();\n        image.depth = checkBitDepth(this.readUint8());\n        const colorType = this.readUint8();\n        this._colorType = colorType;\n        let channels;\n        switch (colorType) {\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.GREYSCALE:\n                channels = 1;\n                break;\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.TRUECOLOUR:\n                channels = 3;\n                break;\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.INDEXED_COLOUR:\n                channels = 1;\n                break;\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.GREYSCALE_ALPHA:\n                channels = 2;\n                break;\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.TRUECOLOUR_ALPHA:\n                channels = 4;\n                break;\n            // Kept for exhaustiveness.\n            // eslint-disable-next-line unicorn/no-useless-switch-case\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.UNKNOWN:\n            default:\n                throw new Error(`Unknown color type: ${colorType}`);\n        }\n        this._png.channels = channels;\n        this._compressionMethod = this.readUint8();\n        if (this._compressionMethod !== _internalTypes__WEBPACK_IMPORTED_MODULE_7__.CompressionMethod.DEFLATE) {\n            throw new Error(`Unsupported compression method: ${this._compressionMethod}`);\n        }\n        this._filterMethod = this.readUint8();\n        this._interlaceMethod = this.readUint8();\n    }\n    decodeACTL() {\n        this._numberOfFrames = this.readUint32();\n        this._numberOfPlays = this.readUint32();\n        this._isAnimated = true;\n    }\n    decodeFCTL() {\n        const image = {\n            sequenceNumber: this.readUint32(),\n            width: this.readUint32(),\n            height: this.readUint32(),\n            xOffset: this.readUint32(),\n            yOffset: this.readUint32(),\n            delayNumber: this.readUint16(),\n            delayDenominator: this.readUint16(),\n            disposeOp: this.readUint8(),\n            blendOp: this.readUint8(),\n            data: new Uint8Array(0),\n        };\n        this._frames.push(image);\n    }\n    // https://www.w3.org/TR/PNG/#11PLTE\n    decodePLTE(length) {\n        if (length % 3 !== 0) {\n            throw new RangeError(`PLTE field length must be a multiple of 3. Got ${length}`);\n        }\n        const l = length / 3;\n        this._hasPalette = true;\n        const palette = [];\n        this._palette = palette;\n        for (let i = 0; i < l; i++) {\n            palette.push([this.readUint8(), this.readUint8(), this.readUint8()]);\n        }\n    }\n    // https://www.w3.org/TR/PNG/#11IDAT\n    decodeIDAT(length) {\n        this._writingDataChunks = true;\n        const dataLength = length;\n        const dataOffset = this.offset + this.byteOffset;\n        this._inflator.push(new Uint8Array(this.buffer, dataOffset, dataLength));\n        if (this._inflator.err) {\n            throw new Error(`Error while decompressing the data: ${this._inflator.err}`);\n        }\n        this.skip(length);\n    }\n    decodeFDAT(length) {\n        this._writingDataChunks = true;\n        let dataLength = length;\n        let dataOffset = this.offset + this.byteOffset;\n        dataOffset += 4;\n        dataLength -= 4;\n        this._inflator.push(new Uint8Array(this.buffer, dataOffset, dataLength));\n        if (this._inflator.err) {\n            throw new Error(`Error while decompressing the data: ${this._inflator.err}`);\n        }\n        this.skip(length);\n    }\n    // https://www.w3.org/TR/PNG/#11tRNS\n    decodetRNS(length) {\n        switch (this._colorType) {\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.GREYSCALE:\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.TRUECOLOUR: {\n                if (length % 2 !== 0) {\n                    throw new RangeError(`tRNS chunk length must be a multiple of 2. Got ${length}`);\n                }\n                if (length / 2 > this._png.width * this._png.height) {\n                    throw new Error(`tRNS chunk contains more alpha values than there are pixels (${length / 2} vs ${this._png.width * this._png.height})`);\n                }\n                this._hasTransparency = true;\n                this._transparency = new Uint16Array(length / 2);\n                for (let i = 0; i < length / 2; i++) {\n                    this._transparency[i] = this.readUint16();\n                }\n                break;\n            }\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.INDEXED_COLOUR: {\n                if (length > this._palette.length) {\n                    throw new Error(`tRNS chunk contains more alpha values than there are palette colors (${length} vs ${this._palette.length})`);\n                }\n                let i = 0;\n                for (; i < length; i++) {\n                    const alpha = this.readByte();\n                    this._palette[i].push(alpha);\n                }\n                for (; i < this._palette.length; i++) {\n                    this._palette[i].push(255);\n                }\n                break;\n            }\n            // Kept for exhaustiveness.\n            /* eslint-disable unicorn/no-useless-switch-case */\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.UNKNOWN:\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.GREYSCALE_ALPHA:\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.ColorType.TRUECOLOUR_ALPHA:\n            default: {\n                throw new Error(`tRNS chunk is not supported for color type ${this._colorType}`);\n            }\n            /* eslint-enable unicorn/no-useless-switch-case */\n        }\n    }\n    // https://www.w3.org/TR/PNG/#11iCCP\n    decodeiCCP(length) {\n        const name = (0,_helpers_text__WEBPACK_IMPORTED_MODULE_6__.readKeyword)(this);\n        const compressionMethod = this.readUint8();\n        if (compressionMethod !== _internalTypes__WEBPACK_IMPORTED_MODULE_7__.CompressionMethod.DEFLATE) {\n            throw new Error(`Unsupported iCCP compression method: ${compressionMethod}`);\n        }\n        const compressedProfile = this.readBytes(length - name.length - 2);\n        this._png.iccEmbeddedProfile = {\n            name,\n            profile: (0,pako__WEBPACK_IMPORTED_MODULE_1__.inflate)(compressedProfile),\n        };\n    }\n    // https://www.w3.org/TR/PNG/#11pHYs\n    decodepHYs() {\n        const ppuX = this.readUint32();\n        const ppuY = this.readUint32();\n        const unitSpecifier = this.readByte();\n        this._png.resolution = { x: ppuX, y: ppuY, unit: unitSpecifier };\n    }\n    decodeApngImage() {\n        this._apng.width = this._png.width;\n        this._apng.height = this._png.height;\n        this._apng.channels = this._png.channels;\n        this._apng.depth = this._png.depth;\n        this._apng.numberOfFrames = this._numberOfFrames;\n        this._apng.numberOfPlays = this._numberOfPlays;\n        this._apng.text = this._png.text;\n        this._apng.resolution = this._png.resolution;\n        for (let i = 0; i < this._numberOfFrames; i++) {\n            const newFrame = {\n                sequenceNumber: this._frames[i].sequenceNumber,\n                delayNumber: this._frames[i].delayNumber,\n                delayDenominator: this._frames[i].delayDenominator,\n                data: this._apng.depth === 8\n                    ? new Uint8Array(this._apng.width * this._apng.height * this._apng.channels)\n                    : new Uint16Array(this._apng.width * this._apng.height * this._apng.channels),\n            };\n            const frame = this._frames.at(i);\n            if (frame) {\n                frame.data = (0,_helpers_decodeInterlaceNull__WEBPACK_IMPORTED_MODULE_4__.decodeInterlaceNull)({\n                    data: frame.data,\n                    width: frame.width,\n                    height: frame.height,\n                    channels: this._apng.channels,\n                    depth: this._apng.depth,\n                });\n                if (this._hasPalette) {\n                    this._apng.palette = this._palette;\n                }\n                if (this._hasTransparency) {\n                    this._apng.transparency = this._transparency;\n                }\n                if (i === 0 ||\n                    (frame.xOffset === 0 &&\n                        frame.yOffset === 0 &&\n                        frame.width === this._png.width &&\n                        frame.height === this._png.height)) {\n                    newFrame.data = frame.data;\n                }\n                else {\n                    const prevFrame = this._apng.frames.at(i - 1);\n                    this.disposeFrame(frame, prevFrame, newFrame);\n                    this.addFrameDataToCanvas(newFrame, frame);\n                }\n                this._apng.frames.push(newFrame);\n            }\n        }\n        return this._apng;\n    }\n    disposeFrame(frame, prevFrame, imageFrame) {\n        switch (frame.disposeOp) {\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.DisposeOpType.NONE:\n                break;\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.DisposeOpType.BACKGROUND:\n                for (let row = 0; row < this._png.height; row++) {\n                    for (let col = 0; col < this._png.width; col++) {\n                        const index = (row * frame.width + col) * this._png.channels;\n                        for (let channel = 0; channel < this._png.channels; channel++) {\n                            imageFrame.data[index + channel] = 0;\n                        }\n                    }\n                }\n                break;\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.DisposeOpType.PREVIOUS:\n                imageFrame.data.set(prevFrame.data);\n                break;\n            default:\n                throw new Error('Unknown disposeOp');\n        }\n    }\n    addFrameDataToCanvas(imageFrame, frame) {\n        const maxValue = 1 << this._png.depth;\n        const calculatePixelIndices = (row, col) => {\n            const index = ((row + frame.yOffset) * this._png.width + frame.xOffset + col) *\n                this._png.channels;\n            const frameIndex = (row * frame.width + col) * this._png.channels;\n            return { index, frameIndex };\n        };\n        switch (frame.blendOp) {\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.BlendOpType.SOURCE:\n                for (let row = 0; row < frame.height; row++) {\n                    for (let col = 0; col < frame.width; col++) {\n                        const { index, frameIndex } = calculatePixelIndices(row, col);\n                        for (let channel = 0; channel < this._png.channels; channel++) {\n                            imageFrame.data[index + channel] =\n                                frame.data[frameIndex + channel];\n                        }\n                    }\n                }\n                break;\n            // https://www.w3.org/TR/png-3/#13Alpha-channel-processing\n            case _internalTypes__WEBPACK_IMPORTED_MODULE_7__.BlendOpType.OVER:\n                for (let row = 0; row < frame.height; row++) {\n                    for (let col = 0; col < frame.width; col++) {\n                        const { index, frameIndex } = calculatePixelIndices(row, col);\n                        for (let channel = 0; channel < this._png.channels; channel++) {\n                            const sourceAlpha = frame.data[frameIndex + this._png.channels - 1] / maxValue;\n                            const foregroundValue = channel % (this._png.channels - 1) === 0\n                                ? 1\n                                : frame.data[frameIndex + channel];\n                            const value = Math.floor(sourceAlpha * foregroundValue +\n                                (1 - sourceAlpha) * imageFrame.data[index + channel]);\n                            imageFrame.data[index + channel] += value;\n                        }\n                    }\n                }\n                break;\n            default:\n                throw new Error('Unknown blendOp');\n        }\n    }\n    decodeImage() {\n        if (this._inflator.err) {\n            throw new Error(`Error while decompressing the data: ${this._inflator.err}`);\n        }\n        const data = this._isAnimated\n            ? (this._frames?.at(0)).data\n            : this._inflator.result;\n        if (this._filterMethod !== _internalTypes__WEBPACK_IMPORTED_MODULE_7__.FilterMethod.ADAPTIVE) {\n            throw new Error(`Filter method ${this._filterMethod} not supported`);\n        }\n        if (this._interlaceMethod === _internalTypes__WEBPACK_IMPORTED_MODULE_7__.InterlaceMethod.NO_INTERLACE) {\n            this._png.data = (0,_helpers_decodeInterlaceNull__WEBPACK_IMPORTED_MODULE_4__.decodeInterlaceNull)({\n                data: data,\n                width: this._png.width,\n                height: this._png.height,\n                channels: this._png.channels,\n                depth: this._png.depth,\n            });\n        }\n        else if (this._interlaceMethod === _internalTypes__WEBPACK_IMPORTED_MODULE_7__.InterlaceMethod.ADAM7) {\n            this._png.data = (0,_helpers_decodeInterlaceAdam7__WEBPACK_IMPORTED_MODULE_3__.decodeInterlaceAdam7)({\n                data: data,\n                width: this._png.width,\n                height: this._png.height,\n                channels: this._png.channels,\n                depth: this._png.depth,\n            });\n        }\n        else {\n            throw new Error(`Interlace method ${this._interlaceMethod} not supported`);\n        }\n        if (this._hasPalette) {\n            this._png.palette = this._palette;\n        }\n        if (this._hasTransparency) {\n            this._png.transparency = this._transparency;\n        }\n    }\n    pushDataToFrame() {\n        const result = this._inflator.result;\n        const lastFrame = this._frames.at(-1);\n        if (lastFrame) {\n            lastFrame.data = result;\n        }\n        else {\n            this._frames.push({\n                sequenceNumber: 0,\n                width: this._png.width,\n                height: this._png.height,\n                xOffset: 0,\n                yOffset: 0,\n                delayNumber: 0,\n                delayDenominator: 0,\n                disposeOp: _internalTypes__WEBPACK_IMPORTED_MODULE_7__.DisposeOpType.NONE,\n                blendOp: _internalTypes__WEBPACK_IMPORTED_MODULE_7__.BlendOpType.SOURCE,\n                data: result,\n            });\n        }\n        this._inflator = new pako__WEBPACK_IMPORTED_MODULE_1__.Inflate();\n        this._writingDataChunks = false;\n    }\n}\nfunction checkBitDepth(value) {\n    if (value !== 1 &&\n        value !== 2 &&\n        value !== 4 &&\n        value !== 8 &&\n        value !== 16) {\n        throw new Error(`invalid bit depth: ${value}`);\n    }\n    return value;\n}\n//# sourceMappingURL=PngDecoder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/PngDecoder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/PngEncoder.js":
/*!*****************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/PngEncoder.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PngEncoder)\n/* harmony export */ });\n/* harmony import */ var iobuffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! iobuffer */ \"(ssr)/./node_modules/iobuffer/lib-esm/IOBuffer.js\");\n/* harmony import */ var pako__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pako */ \"(ssr)/./node_modules/pako/dist/pako.esm.mjs\");\n/* harmony import */ var _helpers_crc__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers/crc */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/crc.js\");\n/* harmony import */ var _helpers_signature__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./helpers/signature */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/signature.js\");\n/* harmony import */ var _helpers_text__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./helpers/text */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/text.js\");\n/* harmony import */ var _internalTypes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./internalTypes */ \"(ssr)/./node_modules/fast-png/lib-esm/internalTypes.js\");\n\n\n\n\n\n\nconst defaultZlibOptions = {\n    level: 3,\n};\nclass PngEncoder extends iobuffer__WEBPACK_IMPORTED_MODULE_0__.IOBuffer {\n    _png;\n    _zlibOptions;\n    _colorType;\n    _interlaceMethod;\n    constructor(data, options = {}) {\n        super();\n        this._colorType = _internalTypes__WEBPACK_IMPORTED_MODULE_5__.ColorType.UNKNOWN;\n        this._zlibOptions = { ...defaultZlibOptions, ...options.zlib };\n        this._png = this._checkData(data);\n        this._interlaceMethod =\n            (options.interlace === 'Adam7'\n                ? _internalTypes__WEBPACK_IMPORTED_MODULE_5__.InterlaceMethod.ADAM7\n                : _internalTypes__WEBPACK_IMPORTED_MODULE_5__.InterlaceMethod.NO_INTERLACE) ?? _internalTypes__WEBPACK_IMPORTED_MODULE_5__.InterlaceMethod.NO_INTERLACE;\n        this.setBigEndian();\n    }\n    encode() {\n        (0,_helpers_signature__WEBPACK_IMPORTED_MODULE_3__.writeSignature)(this);\n        this.encodeIHDR();\n        if (this._png.palette) {\n            this.encodePLTE();\n            if (this._png.palette[0].length === 4) {\n                this.encodeTRNS();\n            }\n        }\n        this.encodeData();\n        if (this._png.text) {\n            for (const [keyword, text] of Object.entries(this._png.text)) {\n                (0,_helpers_text__WEBPACK_IMPORTED_MODULE_4__.encodetEXt)(this, keyword, text);\n            }\n        }\n        this.encodeIEND();\n        return this.toArray();\n    }\n    // https://www.w3.org/TR/PNG/#11IHDR\n    encodeIHDR() {\n        this.writeUint32(13);\n        this.writeChars('IHDR');\n        this.writeUint32(this._png.width);\n        this.writeUint32(this._png.height);\n        this.writeByte(this._png.depth);\n        this.writeByte(this._colorType);\n        this.writeByte(_internalTypes__WEBPACK_IMPORTED_MODULE_5__.CompressionMethod.DEFLATE);\n        this.writeByte(_internalTypes__WEBPACK_IMPORTED_MODULE_5__.FilterMethod.ADAPTIVE);\n        this.writeByte(this._interlaceMethod);\n        (0,_helpers_crc__WEBPACK_IMPORTED_MODULE_2__.writeCrc)(this, 17);\n    }\n    // https://www.w3.org/TR/PNG/#11IEND\n    encodeIEND() {\n        this.writeUint32(0);\n        this.writeChars('IEND');\n        (0,_helpers_crc__WEBPACK_IMPORTED_MODULE_2__.writeCrc)(this, 4);\n    }\n    encodePLTE() {\n        const paletteLength = this._png.palette?.length * 3;\n        this.writeUint32(paletteLength);\n        this.writeChars('PLTE');\n        for (const color of this._png.palette) {\n            this.writeByte(color[0]);\n            this.writeByte(color[1]);\n            this.writeByte(color[2]);\n        }\n        (0,_helpers_crc__WEBPACK_IMPORTED_MODULE_2__.writeCrc)(this, 4 + paletteLength);\n    }\n    encodeTRNS() {\n        const alpha = this._png.palette.filter((color) => {\n            return color.at(-1) !== 255;\n        });\n        this.writeUint32(alpha.length);\n        this.writeChars('tRNS');\n        for (const el of alpha) {\n            this.writeByte(el.at(-1));\n        }\n        (0,_helpers_crc__WEBPACK_IMPORTED_MODULE_2__.writeCrc)(this, 4 + alpha.length);\n    }\n    // https://www.w3.org/TR/PNG/#11IDAT\n    encodeIDAT(data) {\n        this.writeUint32(data.length);\n        this.writeChars('IDAT');\n        this.writeBytes(data);\n        (0,_helpers_crc__WEBPACK_IMPORTED_MODULE_2__.writeCrc)(this, data.length + 4);\n    }\n    encodeData() {\n        const { width, height, channels, depth, data } = this._png;\n        const slotsPerLine = depth <= 8\n            ? Math.ceil((width * depth) / 8) * channels\n            : Math.ceil((((width * depth) / 8) * channels) / 2);\n        const newData = new iobuffer__WEBPACK_IMPORTED_MODULE_0__.IOBuffer().setBigEndian();\n        let offset = 0;\n        if (this._interlaceMethod === _internalTypes__WEBPACK_IMPORTED_MODULE_5__.InterlaceMethod.NO_INTERLACE) {\n            for (let i = 0; i < height; i++) {\n                newData.writeByte(0); // no filter\n                if (depth === 16) {\n                    offset = writeDataUint16(data, newData, slotsPerLine, offset);\n                }\n                else {\n                    offset = writeDataBytes(data, newData, slotsPerLine, offset);\n                }\n            }\n        }\n        else if (this._interlaceMethod === _internalTypes__WEBPACK_IMPORTED_MODULE_5__.InterlaceMethod.ADAM7) {\n            // Adam7 interlacing\n            offset = writeDataInterlaced(this._png, data, newData, offset);\n        }\n        const buffer = newData.toArray();\n        const compressed = (0,pako__WEBPACK_IMPORTED_MODULE_1__.deflate)(buffer, this._zlibOptions);\n        this.encodeIDAT(compressed);\n    }\n    _checkData(data) {\n        const { colorType, channels, depth } = getColorType(data, data.palette);\n        const png = {\n            width: checkInteger(data.width, 'width'),\n            height: checkInteger(data.height, 'height'),\n            channels,\n            data: data.data,\n            depth,\n            text: data.text,\n            palette: data.palette,\n        };\n        this._colorType = colorType;\n        const expectedSize = depth < 8\n            ? Math.ceil((png.width * depth) / 8) * png.height * channels\n            : png.width * png.height * channels;\n        if (png.data.length !== expectedSize) {\n            throw new RangeError(`wrong data size. Found ${png.data.length}, expected ${expectedSize}`);\n        }\n        return png;\n    }\n}\nfunction checkInteger(value, name) {\n    if (Number.isInteger(value) && value > 0) {\n        return value;\n    }\n    throw new TypeError(`${name} must be a positive integer`);\n}\nfunction getColorType(data, palette) {\n    const { channels = 4, depth = 8 } = data;\n    if (channels !== 4 && channels !== 3 && channels !== 2 && channels !== 1) {\n        throw new RangeError(`unsupported number of channels: ${channels}`);\n    }\n    const returnValue = {\n        channels,\n        depth,\n        colorType: _internalTypes__WEBPACK_IMPORTED_MODULE_5__.ColorType.UNKNOWN,\n    };\n    switch (channels) {\n        case 4:\n            returnValue.colorType = _internalTypes__WEBPACK_IMPORTED_MODULE_5__.ColorType.TRUECOLOUR_ALPHA;\n            break;\n        case 3:\n            returnValue.colorType = _internalTypes__WEBPACK_IMPORTED_MODULE_5__.ColorType.TRUECOLOUR;\n            break;\n        case 1:\n            if (palette) {\n                returnValue.colorType = _internalTypes__WEBPACK_IMPORTED_MODULE_5__.ColorType.INDEXED_COLOUR;\n            }\n            else {\n                returnValue.colorType = _internalTypes__WEBPACK_IMPORTED_MODULE_5__.ColorType.GREYSCALE;\n            }\n            break;\n        case 2:\n            returnValue.colorType = _internalTypes__WEBPACK_IMPORTED_MODULE_5__.ColorType.GREYSCALE_ALPHA;\n            break;\n        default:\n            throw new Error('unsupported number of channels');\n    }\n    return returnValue;\n}\nfunction writeDataBytes(data, newData, slotsPerLine, offset) {\n    for (let j = 0; j < slotsPerLine; j++) {\n        newData.writeByte(data[offset++]);\n    }\n    return offset;\n}\nfunction writeDataInterlaced(imageData, data, newData, offset) {\n    const passes = [\n        { x: 0, y: 0, xStep: 8, yStep: 8 },\n        { x: 4, y: 0, xStep: 8, yStep: 8 },\n        { x: 0, y: 4, xStep: 4, yStep: 8 },\n        { x: 2, y: 0, xStep: 4, yStep: 4 },\n        { x: 0, y: 2, xStep: 2, yStep: 4 },\n        { x: 1, y: 0, xStep: 2, yStep: 2 },\n        { x: 0, y: 1, xStep: 1, yStep: 2 },\n    ];\n    const { width, height, channels, depth } = imageData;\n    let pixelSize = 0;\n    if (depth === 16) {\n        pixelSize = (channels * depth) / 8 / 2;\n    }\n    else {\n        pixelSize = (channels * depth) / 8;\n    }\n    // Process each pass\n    for (let passIndex = 0; passIndex < 7; passIndex++) {\n        const pass = passes[passIndex];\n        const passWidth = Math.floor((width - pass.x + pass.xStep - 1) / pass.xStep);\n        const passHeight = Math.floor((height - pass.y + pass.yStep - 1) / pass.yStep);\n        if (passWidth <= 0 || passHeight <= 0)\n            continue;\n        const passLineBytes = passWidth * pixelSize;\n        // For each scanline in this pass\n        for (let y = 0; y < passHeight; y++) {\n            const imageY = pass.y + y * pass.yStep;\n            // Extract raw scanline data\n            const rawScanline = depth <= 8\n                ? new Uint8Array(passLineBytes)\n                : new Uint16Array(passLineBytes);\n            let rawOffset = 0;\n            for (let x = 0; x < passWidth; x++) {\n                const imageX = pass.x + x * pass.xStep;\n                if (imageX < width && imageY < height) {\n                    const srcPos = (imageY * width + imageX) * pixelSize;\n                    for (let i = 0; i < pixelSize; i++) {\n                        rawScanline[rawOffset++] = data[srcPos + i];\n                    }\n                }\n            }\n            newData.writeByte(0); // no filter\n            if (depth === 8) {\n                newData.writeBytes(rawScanline);\n            }\n            else if (depth === 16) {\n                for (const value of rawScanline) {\n                    newData.writeByte((value >> 8) & 0xff); // High byte\n                    newData.writeByte(value & 0xff);\n                }\n            }\n        }\n    }\n    return offset;\n}\nfunction writeDataUint16(data, newData, slotsPerLine, offset) {\n    for (let j = 0; j < slotsPerLine; j++) {\n        newData.writeUint16(data[offset++]);\n    }\n    return offset;\n}\n//# sourceMappingURL=PngEncoder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/PngEncoder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/convertIndexedToRgb.js":
/*!**************************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/convertIndexedToRgb.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertIndexedToRgb: () => (/* binding */ convertIndexedToRgb)\n/* harmony export */ });\n/**\n * Converts indexed data into RGB/RGBA format\n * @param decodedImage - Image to decode data from.\n * @returns Uint8Array with RGB data.\n */\nfunction convertIndexedToRgb(decodedImage) {\n    const palette = decodedImage.palette;\n    const depth = decodedImage.depth;\n    if (!palette) {\n        throw new Error('Color palette is undefined.');\n    }\n    checkDataSize(decodedImage);\n    const indexSize = decodedImage.width * decodedImage.height;\n    const resSize = indexSize * palette[0].length;\n    const res = new Uint8Array(resSize);\n    let indexPos = 0;\n    let offset = 0;\n    const indexes = new Uint8Array(indexSize);\n    let bit = 0xff;\n    switch (depth) {\n        case 1:\n            bit = 0x80;\n            break;\n        case 2:\n            bit = 0xc0;\n            break;\n        case 4:\n            bit = 0xf0;\n            break;\n        case 8:\n            bit = 0xff;\n            break;\n        default:\n            throw new Error('Incorrect depth value');\n    }\n    for (const byte of decodedImage.data) {\n        let bit2 = bit;\n        let shift = 8;\n        while (bit2) {\n            shift -= depth;\n            indexes[indexPos++] = (byte & bit2) >> shift;\n            bit2 = bit2 >> depth;\n            if (indexPos % decodedImage.width === 0) {\n                break;\n            }\n        }\n    }\n    if (decodedImage.palette) {\n        for (const index of indexes) {\n            const color = decodedImage.palette.at(index);\n            if (!color) {\n                throw new Error('Incorrect index of palette color');\n            }\n            res.set(color, offset);\n            offset += color.length;\n        }\n    }\n    return res;\n}\nfunction checkDataSize(image) {\n    const expectedSize = image.depth < 8\n        ? Math.ceil((image.width * image.depth) / 8) *\n            image.height *\n            image.channels\n        : image.width * image.height * image.channels;\n    if (image.data.length !== expectedSize) {\n        throw new RangeError(`wrong data size. Found ${image.data.length}, expected ${expectedSize}`);\n    }\n}\n//# sourceMappingURL=convertIndexedToRgb.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/convertIndexedToRgb.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/helpers/applyUnfilter.js":
/*!****************************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/helpers/applyUnfilter.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyUnfilter: () => (/* binding */ applyUnfilter)\n/* harmony export */ });\n/* harmony import */ var _unfilter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./unfilter */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/unfilter.js\");\n\n/**\n * Apllies filter on scanline based on the filter type.\n * @param filterType - The filter type to apply.\n * @param currentLine - The current line of pixel data.\n * @param newLine - The new line of pixel data.\n * @param prevLine - The previous line of pixel data.\n * @param passLineBytes - The number of bytes in the pass line.\n * @param bytesPerPixel - The number of bytes per pixel.\n */\nfunction applyUnfilter(filterType, currentLine, newLine, prevLine, passLineBytes, bytesPerPixel) {\n    switch (filterType) {\n        case 0:\n            (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterNone)(currentLine, newLine, passLineBytes);\n            break;\n        case 1:\n            (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterSub)(currentLine, newLine, passLineBytes, bytesPerPixel);\n            break;\n        case 2:\n            (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterUp)(currentLine, newLine, prevLine, passLineBytes);\n            break;\n        case 3:\n            (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterAverage)(currentLine, newLine, prevLine, passLineBytes, bytesPerPixel);\n            break;\n        case 4:\n            (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterPaeth)(currentLine, newLine, prevLine, passLineBytes, bytesPerPixel);\n            break;\n        default:\n            throw new Error(`Unsupported filter: ${filterType}`);\n    }\n}\n//# sourceMappingURL=applyUnfilter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/helpers/applyUnfilter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/helpers/crc.js":
/*!******************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/helpers/crc.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkCrc: () => (/* binding */ checkCrc),\n/* harmony export */   writeCrc: () => (/* binding */ writeCrc)\n/* harmony export */ });\nconst crcTable = [];\nfor (let n = 0; n < 256; n++) {\n    let c = n;\n    for (let k = 0; k < 8; k++) {\n        if (c & 1) {\n            c = 0xedb88320 ^ (c >>> 1);\n        }\n        else {\n            c = c >>> 1;\n        }\n    }\n    crcTable[n] = c;\n}\nconst initialCrc = 0xffffffff;\nfunction updateCrc(currentCrc, data, length) {\n    let c = currentCrc;\n    for (let n = 0; n < length; n++) {\n        c = crcTable[(c ^ data[n]) & 0xff] ^ (c >>> 8);\n    }\n    return c;\n}\nfunction crc(data, length) {\n    return (updateCrc(initialCrc, data, length) ^ initialCrc) >>> 0;\n}\nfunction checkCrc(buffer, crcLength, chunkName) {\n    const expectedCrc = buffer.readUint32();\n    const actualCrc = crc(new Uint8Array(buffer.buffer, buffer.byteOffset + buffer.offset - crcLength - 4, crcLength), crcLength); // \"- 4\" because we already advanced by reading the CRC\n    if (actualCrc !== expectedCrc) {\n        throw new Error(`CRC mismatch for chunk ${chunkName}. Expected ${expectedCrc}, found ${actualCrc}`);\n    }\n}\nfunction writeCrc(buffer, length) {\n    buffer.writeUint32(crc(new Uint8Array(buffer.buffer, buffer.byteOffset + buffer.offset - length, length), length));\n}\n//# sourceMappingURL=crc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/helpers/crc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/helpers/decodeInterlaceAdam7.js":
/*!***********************************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/helpers/decodeInterlaceAdam7.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeInterlaceAdam7: () => (/* binding */ decodeInterlaceAdam7)\n/* harmony export */ });\n/* harmony import */ var _applyUnfilter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./applyUnfilter */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/applyUnfilter.js\");\n\nconst uint16 = new Uint16Array([0x00ff]);\nconst uint8 = new Uint8Array(uint16.buffer);\nconst osIsLittleEndian = uint8[0] === 0xff;\n/**\n * Decodes the Adam7 interlaced PNG data.\n *\n * @param params - DecodeInterlaceNullParams\n * @returns - array of pixel data.\n */\nfunction decodeInterlaceAdam7(params) {\n    const { data, width, height, channels, depth } = params;\n    // Adam7 interlacing pattern\n    const passes = [\n        { x: 0, y: 0, xStep: 8, yStep: 8 }, // Pass 1\n        { x: 4, y: 0, xStep: 8, yStep: 8 }, // Pass 2\n        { x: 0, y: 4, xStep: 4, yStep: 8 }, // Pass 3\n        { x: 2, y: 0, xStep: 4, yStep: 4 }, // Pass 4\n        { x: 0, y: 2, xStep: 2, yStep: 4 }, // Pass 5\n        { x: 1, y: 0, xStep: 2, yStep: 2 }, // Pass 6\n        { x: 0, y: 1, xStep: 1, yStep: 2 }, // Pass 7\n    ];\n    const bytesPerPixel = Math.ceil(depth / 8) * channels;\n    const resultData = new Uint8Array(height * width * bytesPerPixel);\n    let offset = 0;\n    // Process each pass\n    for (let passIndex = 0; passIndex < 7; passIndex++) {\n        const pass = passes[passIndex];\n        // Calculate pass dimensions\n        const passWidth = Math.ceil((width - pass.x) / pass.xStep);\n        const passHeight = Math.ceil((height - pass.y) / pass.yStep);\n        if (passWidth <= 0 || passHeight <= 0)\n            continue;\n        const passLineBytes = passWidth * bytesPerPixel;\n        const prevLine = new Uint8Array(passLineBytes);\n        // Process each scanline in this pass\n        for (let y = 0; y < passHeight; y++) {\n            // First byte is the filter type\n            const filterType = data[offset++];\n            const currentLine = data.subarray(offset, offset + passLineBytes);\n            offset += passLineBytes;\n            // Create a new line for the unfiltered data\n            const newLine = new Uint8Array(passLineBytes);\n            // Apply the appropriate unfilter\n            (0,_applyUnfilter__WEBPACK_IMPORTED_MODULE_0__.applyUnfilter)(filterType, currentLine, newLine, prevLine, passLineBytes, bytesPerPixel);\n            prevLine.set(newLine);\n            for (let x = 0; x < passWidth; x++) {\n                const outputX = pass.x + x * pass.xStep;\n                const outputY = pass.y + y * pass.yStep;\n                if (outputX >= width || outputY >= height)\n                    continue;\n                for (let i = 0; i < bytesPerPixel; i++) {\n                    resultData[(outputY * width + outputX) * bytesPerPixel + i] =\n                        newLine[x * bytesPerPixel + i];\n                }\n            }\n        }\n    }\n    if (depth === 16) {\n        const uint16Data = new Uint16Array(resultData.buffer);\n        if (osIsLittleEndian) {\n            for (let k = 0; k < uint16Data.length; k++) {\n                // PNG is always big endian. Swap the bytes.\n                uint16Data[k] = swap16(uint16Data[k]);\n            }\n        }\n        return uint16Data;\n    }\n    else {\n        return resultData;\n    }\n}\nfunction swap16(val) {\n    return ((val & 0xff) << 8) | ((val >> 8) & 0xff);\n}\n//# sourceMappingURL=decodeInterlaceAdam7.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/helpers/decodeInterlaceAdam7.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/helpers/decodeInterlaceNull.js":
/*!**********************************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/helpers/decodeInterlaceNull.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeInterlaceNull: () => (/* binding */ decodeInterlaceNull)\n/* harmony export */ });\n/* harmony import */ var _unfilter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./unfilter */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/unfilter.js\");\n\nconst uint16 = new Uint16Array([0x00ff]);\nconst uint8 = new Uint8Array(uint16.buffer);\nconst osIsLittleEndian = uint8[0] === 0xff;\nconst empty = new Uint8Array(0);\nfunction decodeInterlaceNull(params) {\n    const { data, width, height, channels, depth } = params;\n    const bytesPerPixel = Math.ceil(depth / 8) * channels;\n    const bytesPerLine = Math.ceil((depth / 8) * channels * width);\n    const newData = new Uint8Array(height * bytesPerLine);\n    let prevLine = empty;\n    let offset = 0;\n    let currentLine;\n    let newLine;\n    for (let i = 0; i < height; i++) {\n        currentLine = data.subarray(offset + 1, offset + 1 + bytesPerLine);\n        newLine = newData.subarray(i * bytesPerLine, (i + 1) * bytesPerLine);\n        switch (data[offset]) {\n            case 0:\n                (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterNone)(currentLine, newLine, bytesPerLine);\n                break;\n            case 1:\n                (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterSub)(currentLine, newLine, bytesPerLine, bytesPerPixel);\n                break;\n            case 2:\n                (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterUp)(currentLine, newLine, prevLine, bytesPerLine);\n                break;\n            case 3:\n                (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterAverage)(currentLine, newLine, prevLine, bytesPerLine, bytesPerPixel);\n                break;\n            case 4:\n                (0,_unfilter__WEBPACK_IMPORTED_MODULE_0__.unfilterPaeth)(currentLine, newLine, prevLine, bytesPerLine, bytesPerPixel);\n                break;\n            default:\n                throw new Error(`Unsupported filter: ${data[offset]}`);\n        }\n        prevLine = newLine;\n        offset += bytesPerLine + 1;\n    }\n    if (depth === 16) {\n        const uint16Data = new Uint16Array(newData.buffer);\n        if (osIsLittleEndian) {\n            for (let k = 0; k < uint16Data.length; k++) {\n                // PNG is always big endian. Swap the bytes.\n                uint16Data[k] = swap16(uint16Data[k]);\n            }\n        }\n        return uint16Data;\n    }\n    else {\n        return newData;\n    }\n}\nfunction swap16(val) {\n    return ((val & 0xff) << 8) | ((val >> 8) & 0xff);\n}\n//# sourceMappingURL=decodeInterlaceNull.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/helpers/decodeInterlaceNull.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/helpers/signature.js":
/*!************************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/helpers/signature.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkSignature: () => (/* binding */ checkSignature),\n/* harmony export */   hasPngSignature: () => (/* binding */ hasPngSignature),\n/* harmony export */   writeSignature: () => (/* binding */ writeSignature)\n/* harmony export */ });\n// https://www.w3.org/TR/PNG/#5PNG-file-signature\nconst pngSignature = Uint8Array.of(137, 80, 78, 71, 13, 10, 26, 10);\nfunction writeSignature(buffer) {\n    buffer.writeBytes(pngSignature);\n}\nfunction checkSignature(buffer) {\n    if (!hasPngSignature(buffer.readBytes(pngSignature.length))) {\n        throw new Error('wrong PNG signature');\n    }\n}\nfunction hasPngSignature(array) {\n    if (array.length < pngSignature.length) {\n        return false;\n    }\n    for (let i = 0; i < pngSignature.length; i++) {\n        if (array[i] !== pngSignature[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceMappingURL=signature.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC1wbmcvbGliLWVzbS9oZWxwZXJzL3NpZ25hdHVyZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHlCQUF5QjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29rZG9pLW1hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL2Zhc3QtcG5nL2xpYi1lc20vaGVscGVycy9zaWduYXR1cmUuanM/MDNkMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBodHRwczovL3d3dy53My5vcmcvVFIvUE5HLyM1UE5HLWZpbGUtc2lnbmF0dXJlXG5jb25zdCBwbmdTaWduYXR1cmUgPSBVaW50OEFycmF5Lm9mKDEzNywgODAsIDc4LCA3MSwgMTMsIDEwLCAyNiwgMTApO1xuZXhwb3J0IGZ1bmN0aW9uIHdyaXRlU2lnbmF0dXJlKGJ1ZmZlcikge1xuICAgIGJ1ZmZlci53cml0ZUJ5dGVzKHBuZ1NpZ25hdHVyZSk7XG59XG5leHBvcnQgZnVuY3Rpb24gY2hlY2tTaWduYXR1cmUoYnVmZmVyKSB7XG4gICAgaWYgKCFoYXNQbmdTaWduYXR1cmUoYnVmZmVyLnJlYWRCeXRlcyhwbmdTaWduYXR1cmUubGVuZ3RoKSkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCd3cm9uZyBQTkcgc2lnbmF0dXJlJyk7XG4gICAgfVxufVxuZXhwb3J0IGZ1bmN0aW9uIGhhc1BuZ1NpZ25hdHVyZShhcnJheSkge1xuICAgIGlmIChhcnJheS5sZW5ndGggPCBwbmdTaWduYXR1cmUubGVuZ3RoKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBwbmdTaWduYXR1cmUubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgaWYgKGFycmF5W2ldICE9PSBwbmdTaWduYXR1cmVbaV0pIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNpZ25hdHVyZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/helpers/signature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/helpers/text.js":
/*!*******************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/helpers/text.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodetEXt: () => (/* binding */ decodetEXt),\n/* harmony export */   encodetEXt: () => (/* binding */ encodetEXt),\n/* harmony export */   readKeyword: () => (/* binding */ readKeyword),\n/* harmony export */   readLatin1: () => (/* binding */ readLatin1),\n/* harmony export */   textChunkName: () => (/* binding */ textChunkName)\n/* harmony export */ });\n/* harmony import */ var _crc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crc */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/crc.js\");\n\n// https://www.w3.org/TR/png/#11tEXt\nconst textChunkName = 'tEXt';\nconst NULL = 0;\nconst latin1Decoder = new TextDecoder('latin1');\nfunction validateKeyword(keyword) {\n    validateLatin1(keyword);\n    if (keyword.length === 0 || keyword.length > 79) {\n        throw new Error('keyword length must be between 1 and 79');\n    }\n}\n// eslint-disable-next-line no-control-regex\nconst latin1Regex = /^[\\u0000-\\u00FF]*$/;\nfunction validateLatin1(text) {\n    if (!latin1Regex.test(text)) {\n        throw new Error('invalid latin1 text');\n    }\n}\nfunction decodetEXt(text, buffer, length) {\n    const keyword = readKeyword(buffer);\n    text[keyword] = readLatin1(buffer, length - keyword.length - 1);\n}\nfunction encodetEXt(buffer, keyword, text) {\n    validateKeyword(keyword);\n    validateLatin1(text);\n    const length = keyword.length + 1 /* NULL */ + text.length;\n    buffer.writeUint32(length);\n    buffer.writeChars(textChunkName);\n    buffer.writeChars(keyword);\n    buffer.writeByte(NULL);\n    buffer.writeChars(text);\n    (0,_crc__WEBPACK_IMPORTED_MODULE_0__.writeCrc)(buffer, length + 4);\n}\n// https://www.w3.org/TR/png/#11keywords\nfunction readKeyword(buffer) {\n    buffer.mark();\n    while (buffer.readByte() !== NULL) {\n        /* advance */\n    }\n    const end = buffer.offset;\n    buffer.reset();\n    const keyword = latin1Decoder.decode(buffer.readBytes(end - buffer.offset - 1));\n    // NULL\n    buffer.skip(1);\n    validateKeyword(keyword);\n    return keyword;\n}\nfunction readLatin1(buffer, length) {\n    return latin1Decoder.decode(buffer.readBytes(length));\n}\n//# sourceMappingURL=text.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/helpers/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/helpers/unfilter.js":
/*!***********************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/helpers/unfilter.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unfilterAverage: () => (/* binding */ unfilterAverage),\n/* harmony export */   unfilterNone: () => (/* binding */ unfilterNone),\n/* harmony export */   unfilterPaeth: () => (/* binding */ unfilterPaeth),\n/* harmony export */   unfilterSub: () => (/* binding */ unfilterSub),\n/* harmony export */   unfilterUp: () => (/* binding */ unfilterUp)\n/* harmony export */ });\nfunction unfilterNone(currentLine, newLine, bytesPerLine) {\n    for (let i = 0; i < bytesPerLine; i++) {\n        newLine[i] = currentLine[i];\n    }\n}\nfunction unfilterSub(currentLine, newLine, bytesPerLine, bytesPerPixel) {\n    let i = 0;\n    for (; i < bytesPerPixel; i++) {\n        // just copy first bytes\n        newLine[i] = currentLine[i];\n    }\n    for (; i < bytesPerLine; i++) {\n        newLine[i] = (currentLine[i] + newLine[i - bytesPerPixel]) & 0xff;\n    }\n}\nfunction unfilterUp(currentLine, newLine, prevLine, bytesPerLine) {\n    let i = 0;\n    if (prevLine.length === 0) {\n        // just copy bytes for first line\n        for (; i < bytesPerLine; i++) {\n            newLine[i] = currentLine[i];\n        }\n    }\n    else {\n        for (; i < bytesPerLine; i++) {\n            newLine[i] = (currentLine[i] + prevLine[i]) & 0xff;\n        }\n    }\n}\nfunction unfilterAverage(currentLine, newLine, prevLine, bytesPerLine, bytesPerPixel) {\n    let i = 0;\n    if (prevLine.length === 0) {\n        for (; i < bytesPerPixel; i++) {\n            newLine[i] = currentLine[i];\n        }\n        for (; i < bytesPerLine; i++) {\n            newLine[i] = (currentLine[i] + (newLine[i - bytesPerPixel] >> 1)) & 0xff;\n        }\n    }\n    else {\n        for (; i < bytesPerPixel; i++) {\n            newLine[i] = (currentLine[i] + (prevLine[i] >> 1)) & 0xff;\n        }\n        for (; i < bytesPerLine; i++) {\n            newLine[i] =\n                (currentLine[i] + ((newLine[i - bytesPerPixel] + prevLine[i]) >> 1)) &\n                    0xff;\n        }\n    }\n}\nfunction unfilterPaeth(currentLine, newLine, prevLine, bytesPerLine, bytesPerPixel) {\n    let i = 0;\n    if (prevLine.length === 0) {\n        for (; i < bytesPerPixel; i++) {\n            newLine[i] = currentLine[i];\n        }\n        for (; i < bytesPerLine; i++) {\n            newLine[i] = (currentLine[i] + newLine[i - bytesPerPixel]) & 0xff;\n        }\n    }\n    else {\n        for (; i < bytesPerPixel; i++) {\n            newLine[i] = (currentLine[i] + prevLine[i]) & 0xff;\n        }\n        for (; i < bytesPerLine; i++) {\n            newLine[i] =\n                (currentLine[i] +\n                    paethPredictor(newLine[i - bytesPerPixel], prevLine[i], prevLine[i - bytesPerPixel])) &\n                    0xff;\n        }\n    }\n}\nfunction paethPredictor(a, b, c) {\n    const p = a + b - c;\n    const pa = Math.abs(p - a);\n    const pb = Math.abs(p - b);\n    const pc = Math.abs(p - c);\n    if (pa <= pb && pa <= pc)\n        return a;\n    else if (pb <= pc)\n        return b;\n    else\n        return c;\n}\n//# sourceMappingURL=unfilter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/helpers/unfilter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/index.js":
/*!************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResolutionUnitSpecifier: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_3__.ResolutionUnitSpecifier),\n/* harmony export */   convertIndexedToRgb: () => (/* reexport safe */ _convertIndexedToRgb__WEBPACK_IMPORTED_MODULE_4__.convertIndexedToRgb),\n/* harmony export */   decode: () => (/* binding */ decodePng),\n/* harmony export */   decodeApng: () => (/* binding */ decodeApng),\n/* harmony export */   encode: () => (/* binding */ encodePng),\n/* harmony export */   hasPngSignature: () => (/* reexport safe */ _helpers_signature__WEBPACK_IMPORTED_MODULE_2__.hasPngSignature)\n/* harmony export */ });\n/* harmony import */ var _PngDecoder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PngDecoder */ \"(ssr)/./node_modules/fast-png/lib-esm/PngDecoder.js\");\n/* harmony import */ var _PngEncoder__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PngEncoder */ \"(ssr)/./node_modules/fast-png/lib-esm/PngEncoder.js\");\n/* harmony import */ var _helpers_signature__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers/signature */ \"(ssr)/./node_modules/fast-png/lib-esm/helpers/signature.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/fast-png/lib-esm/types.js\");\n/* harmony import */ var _convertIndexedToRgb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./convertIndexedToRgb */ \"(ssr)/./node_modules/fast-png/lib-esm/convertIndexedToRgb.js\");\n\n\n\n\nfunction decodePng(data, options) {\n    const decoder = new _PngDecoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"](data, options);\n    return decoder.decode();\n}\nfunction encodePng(png, options) {\n    const encoder = new _PngEncoder__WEBPACK_IMPORTED_MODULE_1__[\"default\"](png, options);\n    return encoder.encode();\n}\nfunction decodeApng(data, options) {\n    const decoder = new _PngDecoder__WEBPACK_IMPORTED_MODULE_0__[\"default\"](data, options);\n    return decoder.decodeApng();\n}\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC1wbmcvbGliLWVzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFzQztBQUNBO0FBQ2dCO0FBQzlCO0FBQ3hCO0FBQ0Esd0JBQXdCLG1EQUFVO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixtREFBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsbURBQVU7QUFDbEM7QUFDQTtBQUNnRTtBQUNKO0FBQzVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2tkb2ktbWFya2V0cGxhY2UvLi9ub2RlX21vZHVsZXMvZmFzdC1wbmcvbGliLWVzbS9pbmRleC5qcz85NjVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBQbmdEZWNvZGVyIGZyb20gJy4vUG5nRGVjb2Rlcic7XG5pbXBvcnQgUG5nRW5jb2RlciBmcm9tICcuL1BuZ0VuY29kZXInO1xuZXhwb3J0IHsgaGFzUG5nU2lnbmF0dXJlIH0gZnJvbSAnLi9oZWxwZXJzL3NpZ25hdHVyZSc7XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbmZ1bmN0aW9uIGRlY29kZVBuZyhkYXRhLCBvcHRpb25zKSB7XG4gICAgY29uc3QgZGVjb2RlciA9IG5ldyBQbmdEZWNvZGVyKGRhdGEsIG9wdGlvbnMpO1xuICAgIHJldHVybiBkZWNvZGVyLmRlY29kZSgpO1xufVxuZnVuY3Rpb24gZW5jb2RlUG5nKHBuZywgb3B0aW9ucykge1xuICAgIGNvbnN0IGVuY29kZXIgPSBuZXcgUG5nRW5jb2RlcihwbmcsIG9wdGlvbnMpO1xuICAgIHJldHVybiBlbmNvZGVyLmVuY29kZSgpO1xufVxuZnVuY3Rpb24gZGVjb2RlQXBuZyhkYXRhLCBvcHRpb25zKSB7XG4gICAgY29uc3QgZGVjb2RlciA9IG5ldyBQbmdEZWNvZGVyKGRhdGEsIG9wdGlvbnMpO1xuICAgIHJldHVybiBkZWNvZGVyLmRlY29kZUFwbmcoKTtcbn1cbmV4cG9ydCB7IGRlY29kZVBuZyBhcyBkZWNvZGUsIGVuY29kZVBuZyBhcyBlbmNvZGUsIGRlY29kZUFwbmcgfTtcbmV4cG9ydCB7IGNvbnZlcnRJbmRleGVkVG9SZ2IgfSBmcm9tICcuL2NvbnZlcnRJbmRleGVkVG9SZ2InO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/internalTypes.js":
/*!********************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/internalTypes.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlendOpType: () => (/* binding */ BlendOpType),\n/* harmony export */   ColorType: () => (/* binding */ ColorType),\n/* harmony export */   CompressionMethod: () => (/* binding */ CompressionMethod),\n/* harmony export */   DisposeOpType: () => (/* binding */ DisposeOpType),\n/* harmony export */   FilterMethod: () => (/* binding */ FilterMethod),\n/* harmony export */   InterlaceMethod: () => (/* binding */ InterlaceMethod)\n/* harmony export */ });\nconst ColorType = {\n    UNKNOWN: -1,\n    GREYSCALE: 0,\n    TRUECOLOUR: 2,\n    INDEXED_COLOUR: 3,\n    GREYSCALE_ALPHA: 4,\n    TRUECOLOUR_ALPHA: 6,\n};\nconst CompressionMethod = {\n    UNKNOWN: -1,\n    DEFLATE: 0,\n};\nconst FilterMethod = {\n    UNKNOWN: -1,\n    ADAPTIVE: 0,\n};\nconst InterlaceMethod = {\n    UNKNOWN: -1,\n    NO_INTERLACE: 0,\n    ADAM7: 1,\n};\nconst DisposeOpType = {\n    NONE: 0,\n    BACKGROUND: 1,\n    PREVIOUS: 2,\n};\nconst BlendOpType = {\n    SOURCE: 0,\n    OVER: 1,\n};\n//# sourceMappingURL=internalTypes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC1wbmcvbGliLWVzbS9pbnRlcm5hbFR5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2tkb2ktbWFya2V0cGxhY2UvLi9ub2RlX21vZHVsZXMvZmFzdC1wbmcvbGliLWVzbS9pbnRlcm5hbFR5cGVzLmpzP2U2OGIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IENvbG9yVHlwZSA9IHtcbiAgICBVTktOT1dOOiAtMSxcbiAgICBHUkVZU0NBTEU6IDAsXG4gICAgVFJVRUNPTE9VUjogMixcbiAgICBJTkRFWEVEX0NPTE9VUjogMyxcbiAgICBHUkVZU0NBTEVfQUxQSEE6IDQsXG4gICAgVFJVRUNPTE9VUl9BTFBIQTogNixcbn07XG5leHBvcnQgY29uc3QgQ29tcHJlc3Npb25NZXRob2QgPSB7XG4gICAgVU5LTk9XTjogLTEsXG4gICAgREVGTEFURTogMCxcbn07XG5leHBvcnQgY29uc3QgRmlsdGVyTWV0aG9kID0ge1xuICAgIFVOS05PV046IC0xLFxuICAgIEFEQVBUSVZFOiAwLFxufTtcbmV4cG9ydCBjb25zdCBJbnRlcmxhY2VNZXRob2QgPSB7XG4gICAgVU5LTk9XTjogLTEsXG4gICAgTk9fSU5URVJMQUNFOiAwLFxuICAgIEFEQU03OiAxLFxufTtcbmV4cG9ydCBjb25zdCBEaXNwb3NlT3BUeXBlID0ge1xuICAgIE5PTkU6IDAsXG4gICAgQkFDS0dST1VORDogMSxcbiAgICBQUkVWSU9VUzogMixcbn07XG5leHBvcnQgY29uc3QgQmxlbmRPcFR5cGUgPSB7XG4gICAgU09VUkNFOiAwLFxuICAgIE9WRVI6IDEsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW50ZXJuYWxUeXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/internalTypes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-png/lib-esm/types.js":
/*!************************************************!*\
  !*** ./node_modules/fast-png/lib-esm/types.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResolutionUnitSpecifier: () => (/* binding */ ResolutionUnitSpecifier)\n/* harmony export */ });\nvar ResolutionUnitSpecifier;\n(function (ResolutionUnitSpecifier) {\n    /**\n     * Unit is unknown\n     */\n    ResolutionUnitSpecifier[ResolutionUnitSpecifier[\"UNKNOWN\"] = 0] = \"UNKNOWN\";\n    /**\n     * Unit is the metre\n     */\n    ResolutionUnitSpecifier[ResolutionUnitSpecifier[\"METRE\"] = 1] = \"METRE\";\n})(ResolutionUnitSpecifier || (ResolutionUnitSpecifier = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC1wbmcvbGliLWVzbS90eXBlcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDBEQUEwRDtBQUMzRCIsInNvdXJjZXMiOlsid2VicGFjazovL29rZG9pLW1hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL2Zhc3QtcG5nL2xpYi1lc20vdHlwZXMuanM/NmE1MSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIFJlc29sdXRpb25Vbml0U3BlY2lmaWVyO1xuKGZ1bmN0aW9uIChSZXNvbHV0aW9uVW5pdFNwZWNpZmllcikge1xuICAgIC8qKlxuICAgICAqIFVuaXQgaXMgdW5rbm93blxuICAgICAqL1xuICAgIFJlc29sdXRpb25Vbml0U3BlY2lmaWVyW1Jlc29sdXRpb25Vbml0U3BlY2lmaWVyW1wiVU5LTk9XTlwiXSA9IDBdID0gXCJVTktOT1dOXCI7XG4gICAgLyoqXG4gICAgICogVW5pdCBpcyB0aGUgbWV0cmVcbiAgICAgKi9cbiAgICBSZXNvbHV0aW9uVW5pdFNwZWNpZmllcltSZXNvbHV0aW9uVW5pdFNwZWNpZmllcltcIk1FVFJFXCJdID0gMV0gPSBcIk1FVFJFXCI7XG59KShSZXNvbHV0aW9uVW5pdFNwZWNpZmllciB8fCAoUmVzb2x1dGlvblVuaXRTcGVjaWZpZXIgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-png/lib-esm/types.js\n");

/***/ })

};
;