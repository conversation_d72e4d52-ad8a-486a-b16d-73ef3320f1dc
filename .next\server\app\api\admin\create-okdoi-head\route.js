"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/create-okdoi-head/route";
exports.ids = ["app/api/admin/create-okdoi-head/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_create_okdoi_head_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/create-okdoi-head/route.ts */ \"(rsc)/./src/app/api/admin/create-okdoi-head/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/create-okdoi-head/route\",\n        pathname: \"/api/admin/create-okdoi-head\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/create-okdoi-head/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\create-okdoi-head\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_create_okdoi_head_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/create-okdoi-head/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/create-okdoi-head/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/admin/create-okdoi-head/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/referralSystem */ \"(rsc)/./src/lib/services/referralSystem.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin using admin client\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Check if OKDOI Head already exists\n        const existingHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.getOKDOIHead();\n        if (existingHead) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"OKDOI Head already exists\",\n                data: existingHead\n            }, {\n                status: 400\n            });\n        }\n        // Parse request body for custom details (optional)\n        const body = await request.json().catch(()=>({}));\n        const { email, fullName, phone, userId } = body;\n        let okdoiHead;\n        if (userId) {\n            // Assign OKDOI Head to existing user\n            okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.assignOKDOIHeadToUser(userId);\n        } else {\n            // Create new OKDOI Head user\n            okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.createOKDOIHead(email || \"<EMAIL>\", fullName || \"OKDOI Head\", phone);\n        }\n        console.log(`Admin ${user.email} created OKDOI Head: ${okdoiHead.email}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"OKDOI Head created successfully\",\n            data: okdoiHead\n        });\n    } catch (error) {\n        console.error(\"Error creating OKDOI Head:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create OKDOI Head\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Get OKDOI Head user\n        const okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.getOKDOIHead();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: okdoiHead\n        });\n    } catch (error) {\n        console.error(\"Error getting OKDOI Head:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to get OKDOI Head\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/create-okdoi-head/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/referralSystem.ts":
/*!********************************************!*\
  !*** ./src/lib/services/referralSystem.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReferralSystemService: () => (/* binding */ ReferralSystemService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n/**\n * ReferralSystemService - Manages the multi-level referral and commission system\n */ class ReferralSystemService {\n    /**\n   * Generate a unique referral code\n   */ static async generateReferralCode() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"generate_referral_code\");\n        if (error) {\n            throw new Error(`Failed to generate referral code: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Validate referral code and get referrer information\n   */ static async validateReferralCode(code) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"referral_code\", code).eq(\"is_referral_active\", true).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // Code not found\n                    ;\n                }\n                throw new Error(`Failed to validate referral code: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error validating referral code:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Place user in referral hierarchy\n   */ static async placeUserInHierarchy(newUserId, referrerId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"place_user_in_hierarchy\", {\n                new_user_id: newUserId,\n                referrer_id: referrerId\n            });\n            if (error) {\n                throw new Error(`Failed to place user in hierarchy: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error placing user in hierarchy:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral statistics\n   */ static async getUserReferralStats(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"direct_referrals_count, total_downline_count, total_commission_earned, referral_level, referral_code\").eq(\"id\", userId).single();\n            if (error) {\n                throw new Error(`Failed to get referral stats: ${error.message}`);\n            }\n            let referralCode = data.referral_code;\n            // If user doesn't have a referral code, generate one\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n                // Update the user with the new referral code\n                const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                    referral_code: referralCode\n                }).eq(\"id\", userId);\n                if (updateError) {\n                    console.error(\"Error updating referral code:\", updateError);\n                // Don't throw error, just use the generated code\n                }\n            }\n            return {\n                directReferrals: data.direct_referrals_count || 0,\n                totalDownline: data.total_downline_count || 0,\n                totalCommissionEarned: parseFloat(data.total_commission_earned) || 0,\n                currentLevel: data.referral_level || 0,\n                totalReferrals: data.direct_referrals_count || 0,\n                referralCode: referralCode\n            };\n        } catch (error) {\n            console.error(\"Error getting referral stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's direct referrals\n   */ static async getDirectReferrals(userId) {\n        try {\n            // First get the placement records\n            const { data: placements, error: placementError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"child_id, position, created_at\").eq(\"parent_id\", userId).order(\"position\");\n            if (placementError) {\n                throw new Error(`Failed to get referral placements: ${placementError.message}`);\n            }\n            if (!placements || placements.length === 0) {\n                return [];\n            }\n            // Get the user details for each child\n            const childIds = placements.map((p)=>p.child_id);\n            const { data: users, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").in(\"id\", childIds);\n            if (userError) {\n                throw new Error(`Failed to get referral users: ${userError.message}`);\n            }\n            // Sort users by placement position\n            const sortedUsers = users?.sort((a, b)=>{\n                const positionA = placements.find((p)=>p.child_id === a.id)?.position || 0;\n                const positionB = placements.find((p)=>p.child_id === b.id)?.position || 0;\n                return positionA - positionB;\n            }) || [];\n            return sortedUsers;\n        } catch (error) {\n            console.error(\"Error getting direct referrals:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral tree (up to specified depth)\n   */ static async getReferralTree(userId, maxDepth = 3) {\n        try {\n            // Get the root user\n            const { data: rootUser, error: rootError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (rootError) {\n                throw new Error(`Failed to get root user: ${rootError.message}`);\n            }\n            // Build the tree recursively using referral_placements (tree structure)\n            const buildTree = async (user, currentDepth)=>{\n                const children = [];\n                if (currentDepth < maxDepth) {\n                    const { data: placements, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(`\n              position,\n              placement_type,\n              child:child_id(\n                *,\n                active_subscription:user_subscriptions!left(\n                  id,\n                  status,\n                  expires_at,\n                  package:subscription_packages(\n                    name,\n                    price,\n                    currency\n                  )\n                )\n              )\n            `).eq(\"parent_id\", user.id).order(\"position\");\n                    if (!error && placements) {\n                        for (const placement of placements){\n                            const childNode = await buildTree(placement.child, currentDepth + 1);\n                            childNode.position = placement.position;\n                            children.push(childNode);\n                        }\n                    }\n                }\n                return {\n                    user,\n                    children,\n                    level: currentDepth,\n                    position: 0 // Will be set by parent\n                };\n            };\n            return await buildTree(rootUser, 0);\n        } catch (error) {\n            console.error(\"Error getting referral tree:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's commission transactions\n   */ static async getCommissionTransactions(userId, page = 1, limit = 20) {\n        try {\n            const offset = (page - 1) * limit;\n            const [dataResult, countResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId).order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"beneficiary_id\", userId)\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count commission transactions: ${countResult.error.message}`);\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create Zonal Manager\n   */ static async createZonalManager(userId, zoneName, zoneDescription, assignedDistricts = [], createdBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"zonal_manager\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create zonal manager record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).insert({\n                user_id: userId,\n                zone_name: zoneName,\n                zone_description: zoneDescription,\n                assigned_districts: assignedDistricts,\n                created_by: createdBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create zonal manager: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upgrade user to RSM\n   */ static async upgradeToRSM(userId, zonalManagerId, regionName, upgradedBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"rsm\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create RSM record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).insert({\n                user_id: userId,\n                zonal_manager_id: zonalManagerId,\n                region_name: regionName,\n                upgraded_by: upgradedBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create RSM: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error upgrading to RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Zonal Managers\n   */ static async getZonalManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(`\n          *,\n          user:user_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get zonal managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting zonal managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Regional Sales Managers\n   */ static async getRegionalSalesManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n          *,\n          user:user_id(*),\n          zonal_manager:zonal_manager_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get regional sales managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting regional sales managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Search users for ZM/RSM creation\n   */ static async searchUsers(searchTerm, limit = 20) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`).eq(\"user_type\", \"user\") // Only regular users can be upgraded\n            .limit(limit);\n            if (error) {\n                throw new Error(`Failed to search users: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error searching users:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get referral system statistics\n   */ static async getReferralSystemStats() {\n        try {\n            const [totalUsersResult, zonalManagersResult, regionalManagersResult, totalReferralsResult, activeCodesResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).not(\"referral_code\", \"is\", null)\n            ]);\n            return {\n                totalUsers: totalUsersResult.count || 0,\n                zonalManagers: zonalManagersResult.count || 0,\n                regionalManagers: regionalManagersResult.count || 0,\n                totalReferrals: totalReferralsResult.count || 0,\n                activeReferralCodes: activeCodesResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting referral system stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Zonal Manager\n   */ static async deactivateZonalManager(zmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", zmId);\n            if (error) {\n                throw new Error(`Failed to deactivate zonal manager: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Regional Sales Manager\n   */ static async deactivateRSM(rsmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", rsmId);\n            if (error) {\n                throw new Error(`Failed to deactivate RSM: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create or get OKDOI Head user\n   */ static async createOKDOIHead(email = \"<EMAIL>\", fullName = \"OKDOI Head\", phone) {\n        try {\n            // Check if OKDOI Head already exists using admin client\n            const { data: existingHead, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (existingHead && !checkError) {\n                return existingHead;\n            }\n            // Generate referral code first\n            const referralCode = await this.generateReferralCode();\n            // Create auth user first using admin auth API\n            const { data: authUser, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.createUser({\n                email,\n                password: crypto.randomUUID(),\n                email_confirm: true,\n                user_metadata: {\n                    full_name: fullName,\n                    phone: phone || null\n                }\n            });\n            if (authError || !authUser.user) {\n                throw new Error(`Failed to create auth user: ${authError?.message}`);\n            }\n            // Create OKDOI Head user in public.users table using the auth user ID\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).insert({\n                id: authUser.user.id,\n                email,\n                full_name: fullName,\n                phone,\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                // If user creation fails, clean up the auth user\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(authUser.user.id);\n                throw new Error(`Failed to create OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Assign OKDOI Head role to existing user\n   */ static async assignOKDOIHeadToUser(userId) {\n        try {\n            // Check if OKDOI Head already exists\n            const existingHead = await this.getOKDOIHead();\n            if (existingHead) {\n                throw new Error(\"OKDOI Head already exists. Only one OKDOI Head is allowed.\");\n            }\n            // Get the user to be assigned\n            const { data: user, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (userError || !user) {\n                throw new Error(\"User not found\");\n            }\n            // Generate referral code if user doesn't have one\n            let referralCode = user.referral_code;\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n            }\n            // Update user to OKDOI Head\n            const { data: updatedUser, error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", userId).select().single();\n            if (updateError) {\n                throw new Error(`Failed to assign OKDOI Head: ${updateError.message}`);\n            }\n            return updatedUser;\n        } catch (error) {\n            console.error(\"Error assigning OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get OKDOI Head user\n   */ static async getOKDOIHead() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // No OKDOI Head found\n                    ;\n                }\n                throw new Error(`Failed to get OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting OKDOI Head:\", error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL3JlZmVycmFsU3lzdGVtLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdFO0FBbUJoRTs7Q0FFQyxHQUNNLE1BQU1HO0lBQ1g7O0dBRUMsR0FDRCxhQUFhQyx1QkFBd0M7UUFDbkQsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1OLG1EQUFRQSxDQUFDTyxHQUFHLENBQUM7UUFFM0MsSUFBSUQsT0FBTztZQUNULE1BQU0sSUFBSUUsTUFBTSxDQUFDLGtDQUFrQyxFQUFFRixNQUFNRyxPQUFPLENBQUMsQ0FBQztRQUN0RTtRQUVBLE9BQU9KO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWFLLHFCQUFxQkMsSUFBWSxFQUF3QjtRQUNwRSxJQUFJO1lBQ0YsTUFBTSxFQUFFTixJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1OLG1EQUFRQSxDQUNuQ1ksSUFBSSxDQUFDVixpREFBTUEsQ0FBQ1csS0FBSyxFQUNqQkMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxpQkFBaUJKLE1BQ3BCSSxFQUFFLENBQUMsc0JBQXNCLE1BQ3pCQyxNQUFNO1lBRVQsSUFBSVYsT0FBTztnQkFDVCxJQUFJQSxNQUFNSyxJQUFJLEtBQUssWUFBWTtvQkFDN0IsT0FBTyxLQUFLLGlCQUFpQjs7Z0JBQy9CO2dCQUNBLE1BQU0sSUFBSUgsTUFBTSxDQUFDLGtDQUFrQyxFQUFFRixNQUFNRyxPQUFPLENBQUMsQ0FBQztZQUN0RTtZQUVBLE9BQU9KO1FBQ1QsRUFBRSxPQUFPQyxPQUFPO1lBQ2RXLFFBQVFYLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYVkscUJBQXFCQyxTQUFpQixFQUFFQyxVQUFrQixFQUFpQjtRQUN0RixJQUFJO1lBQ0YsTUFBTSxFQUFFZCxLQUFLLEVBQUUsR0FBRyxNQUFNTixtREFBUUEsQ0FBQ08sR0FBRyxDQUFDLDJCQUEyQjtnQkFDOURjLGFBQWFGO2dCQUNiRyxhQUFhRjtZQUNmO1lBRUEsSUFBSWQsT0FBTztnQkFDVCxNQUFNLElBQUlFLE1BQU0sQ0FBQyxtQ0FBbUMsRUFBRUYsTUFBTUcsT0FBTyxDQUFDLENBQUM7WUFDdkU7UUFDRixFQUFFLE9BQU9ILE9BQU87WUFDZFcsUUFBUVgsS0FBSyxDQUFDLG9DQUFvQ0E7WUFDbEQsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhaUIscUJBQXFCQyxNQUFjLEVBQTBCO1FBQ3hFLElBQUk7WUFDRixNQUFNLEVBQUVuQixJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1OLG1EQUFRQSxDQUNuQ1ksSUFBSSxDQUFDVixpREFBTUEsQ0FBQ1csS0FBSyxFQUNqQkMsTUFBTSxDQUFDLHdHQUNQQyxFQUFFLENBQUMsTUFBTVMsUUFDVFIsTUFBTTtZQUVULElBQUlWLE9BQU87Z0JBQ1QsTUFBTSxJQUFJRSxNQUFNLENBQUMsOEJBQThCLEVBQUVGLE1BQU1HLE9BQU8sQ0FBQyxDQUFDO1lBQ2xFO1lBRUEsSUFBSWdCLGVBQWVwQixLQUFLcUIsYUFBYTtZQUVyQyxxREFBcUQ7WUFDckQsSUFBSSxDQUFDRCxjQUFjO2dCQUNqQkEsZUFBZSxNQUFNLElBQUksQ0FBQ3JCLG9CQUFvQjtnQkFFOUMsNkNBQTZDO2dCQUM3QyxNQUFNLEVBQUVFLE9BQU9xQixXQUFXLEVBQUUsR0FBRyxNQUFNM0IsbURBQVFBLENBQzFDWSxJQUFJLENBQUNWLGlEQUFNQSxDQUFDVyxLQUFLLEVBQ2pCZSxNQUFNLENBQUM7b0JBQUVGLGVBQWVEO2dCQUFhLEdBQ3JDVixFQUFFLENBQUMsTUFBTVM7Z0JBRVosSUFBSUcsYUFBYTtvQkFDZlYsUUFBUVgsS0FBSyxDQUFDLGlDQUFpQ3FCO2dCQUMvQyxpREFBaUQ7Z0JBQ25EO1lBQ0Y7WUFFQSxPQUFPO2dCQUNMRSxpQkFBaUJ4QixLQUFLeUIsc0JBQXNCLElBQUk7Z0JBQ2hEQyxlQUFlMUIsS0FBSzJCLG9CQUFvQixJQUFJO2dCQUM1Q0MsdUJBQXVCQyxXQUFXN0IsS0FBSzhCLHVCQUF1QixLQUFLO2dCQUNuRUMsY0FBYy9CLEtBQUtnQyxjQUFjLElBQUk7Z0JBQ3JDQyxnQkFBZ0JqQyxLQUFLeUIsc0JBQXNCLElBQUk7Z0JBQy9DTCxjQUFjQTtZQUNoQjtRQUNGLEVBQUUsT0FBT25CLE9BQU87WUFDZFcsUUFBUVgsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0MsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhaUMsbUJBQW1CZixNQUFjLEVBQW1CO1FBQy9ELElBQUk7WUFDRixrQ0FBa0M7WUFDbEMsTUFBTSxFQUFFbkIsTUFBTW1DLFVBQVUsRUFBRWxDLE9BQU9tQyxjQUFjLEVBQUUsR0FBRyxNQUFNeEMsd0RBQWFBLENBQ3BFVyxJQUFJLENBQUNWLGlEQUFNQSxDQUFDd0MsbUJBQW1CLEVBQy9CNUIsTUFBTSxDQUFDLGtDQUNQQyxFQUFFLENBQUMsYUFBYVMsUUFDaEJtQixLQUFLLENBQUM7WUFFVCxJQUFJRixnQkFBZ0I7Z0JBQ2xCLE1BQU0sSUFBSWpDLE1BQU0sQ0FBQyxtQ0FBbUMsRUFBRWlDLGVBQWVoQyxPQUFPLENBQUMsQ0FBQztZQUNoRjtZQUVBLElBQUksQ0FBQytCLGNBQWNBLFdBQVdJLE1BQU0sS0FBSyxHQUFHO2dCQUMxQyxPQUFPLEVBQUU7WUFDWDtZQUVBLHNDQUFzQztZQUN0QyxNQUFNQyxXQUFXTCxXQUFXTSxHQUFHLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLFFBQVE7WUFDL0MsTUFBTSxFQUFFM0MsTUFBTTRDLEtBQUssRUFBRTNDLE9BQU80QyxTQUFTLEVBQUUsR0FBRyxNQUFNakQsd0RBQWFBLENBQzFEVyxJQUFJLENBQUNWLGlEQUFNQSxDQUFDVyxLQUFLLEVBQ2pCQyxNQUFNLENBQUMsS0FDUHFDLEVBQUUsQ0FBQyxNQUFNTjtZQUVaLElBQUlLLFdBQVc7Z0JBQ2IsTUFBTSxJQUFJMUMsTUFBTSxDQUFDLDhCQUE4QixFQUFFMEMsVUFBVXpDLE9BQU8sQ0FBQyxDQUFDO1lBQ3RFO1lBRUEsbUNBQW1DO1lBQ25DLE1BQU0yQyxjQUFjSCxPQUFPSSxLQUFLLENBQUNDLEdBQUdDO2dCQUNsQyxNQUFNQyxZQUFZaEIsV0FBV2lCLElBQUksQ0FBQ1YsQ0FBQUEsSUFBS0EsRUFBRUMsUUFBUSxLQUFLTSxFQUFFSSxFQUFFLEdBQUdDLFlBQVk7Z0JBQ3pFLE1BQU1DLFlBQVlwQixXQUFXaUIsSUFBSSxDQUFDVixDQUFBQSxJQUFLQSxFQUFFQyxRQUFRLEtBQUtPLEVBQUVHLEVBQUUsR0FBR0MsWUFBWTtnQkFDekUsT0FBT0gsWUFBWUk7WUFDckIsTUFBTSxFQUFFO1lBRVIsT0FBT1I7UUFDVCxFQUFFLE9BQU85QyxPQUFPO1lBQ2RXLFFBQVFYLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYXVELGdCQUFnQnJDLE1BQWMsRUFBRXNDLFdBQW1CLENBQUMsRUFBNkI7UUFDNUYsSUFBSTtZQUNGLG9CQUFvQjtZQUNwQixNQUFNLEVBQUV6RCxNQUFNMEQsUUFBUSxFQUFFekQsT0FBTzBELFNBQVMsRUFBRSxHQUFHLE1BQU1oRSxtREFBUUEsQ0FDeERZLElBQUksQ0FBQ1YsaURBQU1BLENBQUNXLEtBQUssRUFDakJDLE1BQU0sQ0FBQyxLQUNQQyxFQUFFLENBQUMsTUFBTVMsUUFDVFIsTUFBTTtZQUVULElBQUlnRCxXQUFXO2dCQUNiLE1BQU0sSUFBSXhELE1BQU0sQ0FBQyx5QkFBeUIsRUFBRXdELFVBQVV2RCxPQUFPLENBQUMsQ0FBQztZQUNqRTtZQUVBLHdFQUF3RTtZQUN4RSxNQUFNd0QsWUFBWSxPQUFPQyxNQUFZQztnQkFDbkMsTUFBTUMsV0FBK0IsRUFBRTtnQkFFdkMsSUFBSUQsZUFBZUwsVUFBVTtvQkFDM0IsTUFBTSxFQUFFekQsTUFBTW1DLFVBQVUsRUFBRWxDLEtBQUssRUFBRSxHQUFHLE1BQU1OLG1EQUFRQSxDQUMvQ1ksSUFBSSxDQUFDVixpREFBTUEsQ0FBQ3dDLG1CQUFtQixFQUMvQjVCLE1BQU0sQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7O1lBZ0JULENBQUMsRUFDQUMsRUFBRSxDQUFDLGFBQWFtRCxLQUFLUixFQUFFLEVBQ3ZCZixLQUFLLENBQUM7b0JBRVQsSUFBSSxDQUFDckMsU0FBU2tDLFlBQVk7d0JBQ3hCLEtBQUssTUFBTTZCLGFBQWE3QixXQUFZOzRCQUNsQyxNQUFNOEIsWUFBWSxNQUFNTCxVQUFVSSxVQUFVRSxLQUFLLEVBQUVKLGVBQWU7NEJBQ2xFRyxVQUFVWCxRQUFRLEdBQUdVLFVBQVVWLFFBQVE7NEJBQ3ZDUyxTQUFTSSxJQUFJLENBQUNGO3dCQUNoQjtvQkFDRjtnQkFDRjtnQkFFQSxPQUFPO29CQUNMSjtvQkFDQUU7b0JBQ0FLLE9BQU9OO29CQUNQUixVQUFVLEVBQUUsd0JBQXdCO2dCQUN0QztZQUNGO1lBRUEsT0FBTyxNQUFNTSxVQUFVRixVQUFVO1FBQ25DLEVBQUUsT0FBT3pELE9BQU87WUFDZFcsUUFBUVgsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUMsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhb0UsMEJBQ1hsRCxNQUFjLEVBQ2RtRCxPQUFlLENBQUMsRUFDaEJDLFFBQWdCLEVBQUUsRUFDaUQ7UUFDbkUsSUFBSTtZQUNGLE1BQU1DLFNBQVMsQ0FBQ0YsT0FBTyxLQUFLQztZQUU1QixNQUFNLENBQUNFLFlBQVlDLFlBQVksR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ2xEakYsbURBQVFBLENBQ0xZLElBQUksQ0FBQ1YsaURBQU1BLENBQUNnRix1QkFBdUIsRUFDbkNwRSxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLGtCQUFrQlMsUUFDckJtQixLQUFLLENBQUMsY0FBYztvQkFBRXdDLFdBQVc7Z0JBQU0sR0FDdkNDLEtBQUssQ0FBQ1AsUUFBUUEsU0FBU0QsUUFBUTtnQkFDbEM1RSxtREFBUUEsQ0FDTFksSUFBSSxDQUFDVixpREFBTUEsQ0FBQ2dGLHVCQUF1QixFQUNuQ3BFLE1BQU0sQ0FBQyxLQUFLO29CQUFFdUUsT0FBTztvQkFBU0MsTUFBTTtnQkFBSyxHQUN6Q3ZFLEVBQUUsQ0FBQyxrQkFBa0JTO2FBQ3pCO1lBRUQsSUFBSXNELFdBQVd4RSxLQUFLLEVBQUU7Z0JBQ3BCLE1BQU0sSUFBSUUsTUFBTSxDQUFDLHVDQUF1QyxFQUFFc0UsV0FBV3hFLEtBQUssQ0FBQ0csT0FBTyxDQUFDLENBQUM7WUFDdEY7WUFFQSxJQUFJc0UsWUFBWXpFLEtBQUssRUFBRTtnQkFDckIsTUFBTSxJQUFJRSxNQUFNLENBQUMseUNBQXlDLEVBQUV1RSxZQUFZekUsS0FBSyxDQUFDRyxPQUFPLENBQUMsQ0FBQztZQUN6RjtZQUVBLE9BQU87Z0JBQ0w4RSxjQUFjVCxXQUFXekUsSUFBSSxJQUFJLEVBQUU7Z0JBQ25DbUYsT0FBT1QsWUFBWU0sS0FBSyxJQUFJO1lBQzlCO1FBQ0YsRUFBRSxPQUFPL0UsT0FBTztZQUNkVyxRQUFRWCxLQUFLLENBQUMsMENBQTBDQTtZQUN4RCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFtRixtQkFDWGpFLE1BQWMsRUFDZGtFLFFBQWdCLEVBQ2hCQyxlQUF3QixFQUN4QkMsb0JBQThCLEVBQUUsRUFDaENDLFNBQWtCLEVBQ0s7UUFDdkIsSUFBSTtZQUNGLHlCQUF5QjtZQUN6QixNQUFNLEVBQUV2RixPQUFPNEMsU0FBUyxFQUFFLEdBQUcsTUFBTWxELG1EQUFRQSxDQUN4Q1ksSUFBSSxDQUFDVixpREFBTUEsQ0FBQ1csS0FBSyxFQUNqQmUsTUFBTSxDQUFDO2dCQUFFa0UsV0FBVztZQUFnQixHQUNwQy9FLEVBQUUsQ0FBQyxNQUFNUztZQUVaLElBQUkwQixXQUFXO2dCQUNiLE1BQU0sSUFBSTFDLE1BQU0sQ0FBQyw0QkFBNEIsRUFBRTBDLFVBQVV6QyxPQUFPLENBQUMsQ0FBQztZQUNwRTtZQUVBLDhCQUE4QjtZQUM5QixNQUFNLEVBQUVKLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTU4sbURBQVFBLENBQ25DWSxJQUFJLENBQUNWLGlEQUFNQSxDQUFDNkYsY0FBYyxFQUMxQkMsTUFBTSxDQUFDO2dCQUNOQyxTQUFTekU7Z0JBQ1QwRSxXQUFXUjtnQkFDWFMsa0JBQWtCUjtnQkFDbEJTLG9CQUFvQlI7Z0JBQ3BCUyxZQUFZUjtZQUNkLEdBQ0MvRSxNQUFNLEdBQ05FLE1BQU07WUFFVCxJQUFJVixPQUFPO2dCQUNULE1BQU0sSUFBSUUsTUFBTSxDQUFDLGdDQUFnQyxFQUFFRixNQUFNRyxPQUFPLENBQUMsQ0FBQztZQUNwRTtZQUVBLE9BQU9KO1FBQ1QsRUFBRSxPQUFPQyxPQUFPO1lBQ2RXLFFBQVFYLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYWdHLGFBQ1g5RSxNQUFjLEVBQ2QrRSxjQUF1QixFQUN2QkMsVUFBbUIsRUFDbkJDLFVBQW1CLEVBQ1k7UUFDL0IsSUFBSTtZQUNGLHlCQUF5QjtZQUN6QixNQUFNLEVBQUVuRyxPQUFPNEMsU0FBUyxFQUFFLEdBQUcsTUFBTWxELG1EQUFRQSxDQUN4Q1ksSUFBSSxDQUFDVixpREFBTUEsQ0FBQ1csS0FBSyxFQUNqQmUsTUFBTSxDQUFDO2dCQUFFa0UsV0FBVztZQUFNLEdBQzFCL0UsRUFBRSxDQUFDLE1BQU1TO1lBRVosSUFBSTBCLFdBQVc7Z0JBQ2IsTUFBTSxJQUFJMUMsTUFBTSxDQUFDLDRCQUE0QixFQUFFMEMsVUFBVXpDLE9BQU8sQ0FBQyxDQUFDO1lBQ3BFO1lBRUEsb0JBQW9CO1lBQ3BCLE1BQU0sRUFBRUosSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTixtREFBUUEsQ0FDbkNZLElBQUksQ0FBQ1YsaURBQU1BLENBQUN3Ryx1QkFBdUIsRUFDbkNWLE1BQU0sQ0FBQztnQkFDTkMsU0FBU3pFO2dCQUNUbUYsa0JBQWtCSjtnQkFDbEJLLGFBQWFKO2dCQUNiSyxhQUFhSjtZQUNmLEdBQ0MzRixNQUFNLEdBQ05FLE1BQU07WUFFVCxJQUFJVixPQUFPO2dCQUNULE1BQU0sSUFBSUUsTUFBTSxDQUFDLHNCQUFzQixFQUFFRixNQUFNRyxPQUFPLENBQUMsQ0FBQztZQUMxRDtZQUVBLE9BQU9KO1FBQ1QsRUFBRSxPQUFPQyxPQUFPO1lBQ2RXLFFBQVFYLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYXdHLG1CQUErRDtRQUMxRSxJQUFJO1lBQ0YsTUFBTSxFQUFFekcsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTixtREFBUUEsQ0FDbkNZLElBQUksQ0FBQ1YsaURBQU1BLENBQUM2RixjQUFjLEVBQzFCakYsTUFBTSxDQUFDLENBQUM7OztRQUdULENBQUMsRUFDQTZCLEtBQUssQ0FBQyxjQUFjO2dCQUFFd0MsV0FBVztZQUFNO1lBRTFDLElBQUk3RSxPQUFPO2dCQUNULE1BQU0sSUFBSUUsTUFBTSxDQUFDLDhCQUE4QixFQUFFRixNQUFNRyxPQUFPLENBQUMsQ0FBQztZQUNsRTtZQUVBLE9BQU9KLFFBQVEsRUFBRTtRQUNuQixFQUFFLE9BQU9DLE9BQU87WUFDZFcsUUFBUVgsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0MsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFheUcsMkJBQTZHO1FBQ3hILElBQUk7WUFDRixNQUFNLEVBQUUxRyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1OLG1EQUFRQSxDQUNuQ1ksSUFBSSxDQUFDVixpREFBTUEsQ0FBQ3dHLHVCQUF1QixFQUNuQzVGLE1BQU0sQ0FBQyxDQUFDOzs7O1FBSVQsQ0FBQyxFQUNBNkIsS0FBSyxDQUFDLGNBQWM7Z0JBQUV3QyxXQUFXO1lBQU07WUFFMUMsSUFBSTdFLE9BQU87Z0JBQ1QsTUFBTSxJQUFJRSxNQUFNLENBQUMsdUNBQXVDLEVBQUVGLE1BQU1HLE9BQU8sQ0FBQyxDQUFDO1lBQzNFO1lBRUEsT0FBT0osUUFBUSxFQUFFO1FBQ25CLEVBQUUsT0FBT0MsT0FBTztZQUNkVyxRQUFRWCxLQUFLLENBQUMsMENBQTBDQTtZQUN4RCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWEwRyxZQUFZQyxVQUFrQixFQUFFckMsUUFBZ0IsRUFBRSxFQUFtQjtRQUNoRixJQUFJO1lBQ0YsTUFBTSxFQUFFdkUsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNTixtREFBUUEsQ0FDbkNZLElBQUksQ0FBQ1YsaURBQU1BLENBQUNXLEtBQUssRUFDakJDLE1BQU0sQ0FBQyxLQUNQb0csRUFBRSxDQUFDLENBQUMsaUJBQWlCLEVBQUVELFdBQVcsZUFBZSxFQUFFQSxXQUFXLENBQUMsQ0FBQyxFQUNoRWxHLEVBQUUsQ0FBQyxhQUFhLFFBQVEscUNBQXFDO2FBQzdENkQsS0FBSyxDQUFDQTtZQUVULElBQUl0RSxPQUFPO2dCQUNULE1BQU0sSUFBSUUsTUFBTSxDQUFDLHdCQUF3QixFQUFFRixNQUFNRyxPQUFPLENBQUMsQ0FBQztZQUM1RDtZQUVBLE9BQU9KLFFBQVEsRUFBRTtRQUNuQixFQUFFLE9BQU9DLE9BQU87WUFDZFcsUUFBUVgsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeEMsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhNkcseUJBTVY7UUFDRCxJQUFJO1lBQ0YsTUFBTSxDQUNKQyxrQkFDQUMscUJBQ0FDLHdCQUNBQyxzQkFDQUMsa0JBQ0QsR0FBRyxNQUFNeEMsUUFBUUMsR0FBRyxDQUFDO2dCQUNwQmpGLG1EQUFRQSxDQUFDWSxJQUFJLENBQUNWLGlEQUFNQSxDQUFDVyxLQUFLLEVBQUVDLE1BQU0sQ0FBQyxLQUFLO29CQUFFdUUsT0FBTztvQkFBU0MsTUFBTTtnQkFBSztnQkFDckV0RixtREFBUUEsQ0FBQ1ksSUFBSSxDQUFDVixpREFBTUEsQ0FBQzZGLGNBQWMsRUFBRWpGLE1BQU0sQ0FBQyxLQUFLO29CQUFFdUUsT0FBTztvQkFBU0MsTUFBTTtnQkFBSyxHQUFHdkUsRUFBRSxDQUFDLGFBQWE7Z0JBQ2pHZixtREFBUUEsQ0FBQ1ksSUFBSSxDQUFDVixpREFBTUEsQ0FBQ3dHLHVCQUF1QixFQUFFNUYsTUFBTSxDQUFDLEtBQUs7b0JBQUV1RSxPQUFPO29CQUFTQyxNQUFNO2dCQUFLLEdBQUd2RSxFQUFFLENBQUMsYUFBYTtnQkFDMUdmLG1EQUFRQSxDQUFDWSxJQUFJLENBQUNWLGlEQUFNQSxDQUFDd0MsbUJBQW1CLEVBQUU1QixNQUFNLENBQUMsS0FBSztvQkFBRXVFLE9BQU87b0JBQVNDLE1BQU07Z0JBQUs7Z0JBQ25GdEYsbURBQVFBLENBQUNZLElBQUksQ0FBQ1YsaURBQU1BLENBQUNXLEtBQUssRUFBRUMsTUFBTSxDQUFDLEtBQUs7b0JBQUV1RSxPQUFPO29CQUFTQyxNQUFNO2dCQUFLLEdBQUdtQyxHQUFHLENBQUMsaUJBQWlCLE1BQU07YUFDcEc7WUFFRCxPQUFPO2dCQUNMQyxZQUFZTixpQkFBaUIvQixLQUFLLElBQUk7Z0JBQ3RDc0MsZUFBZU4sb0JBQW9CaEMsS0FBSyxJQUFJO2dCQUM1Q3VDLGtCQUFrQk4sdUJBQXVCakMsS0FBSyxJQUFJO2dCQUNsRC9DLGdCQUFnQmlGLHFCQUFxQmxDLEtBQUssSUFBSTtnQkFDOUN3QyxxQkFBcUJMLGtCQUFrQm5DLEtBQUssSUFBSTtZQUNsRDtRQUNGLEVBQUUsT0FBTy9FLE9BQU87WUFDZFcsUUFBUVgsS0FBSyxDQUFDLHdDQUF3Q0E7WUFDdEQsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhd0gsdUJBQXVCQyxJQUFZLEVBQWlCO1FBQy9ELElBQUk7WUFDRixNQUFNLEVBQUV6SCxLQUFLLEVBQUUsR0FBRyxNQUFNTixtREFBUUEsQ0FDN0JZLElBQUksQ0FBQ1YsaURBQU1BLENBQUM2RixjQUFjLEVBQzFCbkUsTUFBTSxDQUFDO2dCQUFFb0csV0FBVztZQUFNLEdBQzFCakgsRUFBRSxDQUFDLE1BQU1nSDtZQUVaLElBQUl6SCxPQUFPO2dCQUNULE1BQU0sSUFBSUUsTUFBTSxDQUFDLG9DQUFvQyxFQUFFRixNQUFNRyxPQUFPLENBQUMsQ0FBQztZQUN4RTtRQUNGLEVBQUUsT0FBT0gsT0FBTztZQUNkVyxRQUFRWCxLQUFLLENBQUMscUNBQXFDQTtZQUNuRCxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWEySCxjQUFjQyxLQUFhLEVBQWlCO1FBQ3ZELElBQUk7WUFDRixNQUFNLEVBQUU1SCxLQUFLLEVBQUUsR0FBRyxNQUFNTixtREFBUUEsQ0FDN0JZLElBQUksQ0FBQ1YsaURBQU1BLENBQUN3Ryx1QkFBdUIsRUFDbkM5RSxNQUFNLENBQUM7Z0JBQUVvRyxXQUFXO1lBQU0sR0FDMUJqSCxFQUFFLENBQUMsTUFBTW1IO1lBRVosSUFBSTVILE9BQU87Z0JBQ1QsTUFBTSxJQUFJRSxNQUFNLENBQUMsMEJBQTBCLEVBQUVGLE1BQU1HLE9BQU8sQ0FBQyxDQUFDO1lBQzlEO1FBQ0YsRUFBRSxPQUFPSCxPQUFPO1lBQ2RXLFFBQVFYLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYTZILGdCQUNYQyxRQUFnQixzQkFBc0IsRUFDdENDLFdBQW1CLFlBQVksRUFDL0JDLEtBQWMsRUFDQztRQUNmLElBQUk7WUFDRix3REFBd0Q7WUFDeEQsTUFBTSxFQUFFakksTUFBTWtJLFlBQVksRUFBRWpJLE9BQU9rSSxVQUFVLEVBQUUsR0FBRyxNQUFNdkksd0RBQWFBLENBQ2xFVyxJQUFJLENBQUNWLGlEQUFNQSxDQUFDVyxLQUFLLEVBQ2pCQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLGFBQWEsY0FDaEJDLE1BQU07WUFFVCxJQUFJdUgsZ0JBQWdCLENBQUNDLFlBQVk7Z0JBQy9CLE9BQU9EO1lBQ1Q7WUFFQSwrQkFBK0I7WUFDL0IsTUFBTTlHLGVBQWUsTUFBTSxJQUFJLENBQUNyQixvQkFBb0I7WUFFcEQsOENBQThDO1lBQzlDLE1BQU0sRUFBRUMsTUFBTW9JLFFBQVEsRUFBRW5JLE9BQU9vSSxTQUFTLEVBQUUsR0FBRyxNQUFNekksd0RBQWFBLENBQUMwSSxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsVUFBVSxDQUFDO2dCQUNyRlQ7Z0JBQ0FVLFVBQVVDLE9BQU9DLFVBQVU7Z0JBQzNCQyxlQUFlO2dCQUNmQyxlQUFlO29CQUNiQyxXQUFXZDtvQkFDWEMsT0FBT0EsU0FBUztnQkFDbEI7WUFDRjtZQUVBLElBQUlJLGFBQWEsQ0FBQ0QsU0FBU3ZFLElBQUksRUFBRTtnQkFDL0IsTUFBTSxJQUFJMUQsTUFBTSxDQUFDLDRCQUE0QixFQUFFa0ksV0FBV2pJLFFBQVEsQ0FBQztZQUNyRTtZQUVBLHNFQUFzRTtZQUN0RSxNQUFNLEVBQUVKLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUwsd0RBQWFBLENBQ3hDVyxJQUFJLENBQUNWLGlEQUFNQSxDQUFDVyxLQUFLLEVBQ2pCbUYsTUFBTSxDQUFDO2dCQUNOdEMsSUFBSStFLFNBQVN2RSxJQUFJLENBQUNSLEVBQUU7Z0JBQ3BCMEU7Z0JBQ0FlLFdBQVdkO2dCQUNYQztnQkFDQXhDLFdBQVc7Z0JBQ1hzRCxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxvQkFBb0I7Z0JBQ3BCakgsZ0JBQWdCO2dCQUNoQmtILGVBQWU7Z0JBQ2Z6SCx3QkFBd0I7Z0JBQ3hCRSxzQkFBc0I7Z0JBQ3RCRyx5QkFBeUI7Z0JBQ3pCVCxlQUFlRDtnQkFDZitILFlBQVksSUFBSUMsT0FBT0MsV0FBVztnQkFDbENDLFlBQVksSUFBSUYsT0FBT0MsV0FBVztZQUNwQyxHQUNDNUksTUFBTSxHQUNORSxNQUFNO1lBRVQsSUFBSVYsT0FBTztnQkFDVCxpREFBaUQ7Z0JBQ2pELE1BQU1MLHdEQUFhQSxDQUFDMEksSUFBSSxDQUFDQyxLQUFLLENBQUNnQixVQUFVLENBQUNuQixTQUFTdkUsSUFBSSxDQUFDUixFQUFFO2dCQUMxRCxNQUFNLElBQUlsRCxNQUFNLENBQUMsNkJBQTZCLEVBQUVGLE1BQU1HLE9BQU8sQ0FBQyxDQUFDO1lBQ2pFO1lBRUEsT0FBT0o7UUFDVCxFQUFFLE9BQU9DLE9BQU87WUFDZFcsUUFBUVgsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUMsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhdUosc0JBQXNCckksTUFBYyxFQUFpQjtRQUNoRSxJQUFJO1lBQ0YscUNBQXFDO1lBQ3JDLE1BQU0rRyxlQUFlLE1BQU0sSUFBSSxDQUFDdUIsWUFBWTtZQUM1QyxJQUFJdkIsY0FBYztnQkFDaEIsTUFBTSxJQUFJL0gsTUFBTTtZQUNsQjtZQUVBLDhCQUE4QjtZQUM5QixNQUFNLEVBQUVILE1BQU02RCxJQUFJLEVBQUU1RCxPQUFPNEMsU0FBUyxFQUFFLEdBQUcsTUFBTWpELHdEQUFhQSxDQUN6RFcsSUFBSSxDQUFDVixpREFBTUEsQ0FBQ1csS0FBSyxFQUNqQkMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxNQUFNUyxRQUNUUixNQUFNO1lBRVQsSUFBSWtDLGFBQWEsQ0FBQ2dCLE1BQU07Z0JBQ3RCLE1BQU0sSUFBSTFELE1BQU07WUFDbEI7WUFFQSxrREFBa0Q7WUFDbEQsSUFBSWlCLGVBQWV5QyxLQUFLeEMsYUFBYTtZQUNyQyxJQUFJLENBQUNELGNBQWM7Z0JBQ2pCQSxlQUFlLE1BQU0sSUFBSSxDQUFDckIsb0JBQW9CO1lBQ2hEO1lBRUEsNEJBQTRCO1lBQzVCLE1BQU0sRUFBRUMsTUFBTTBKLFdBQVcsRUFBRXpKLE9BQU9xQixXQUFXLEVBQUUsR0FBRyxNQUFNMUIsd0RBQWFBLENBQ2xFVyxJQUFJLENBQUNWLGlEQUFNQSxDQUFDVyxLQUFLLEVBQ2pCZSxNQUFNLENBQUM7Z0JBQ05rRSxXQUFXO2dCQUNYc0QsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsb0JBQW9CO2dCQUNwQmpILGdCQUFnQjtnQkFDaEJrSCxlQUFlO2dCQUNmekgsd0JBQXdCO2dCQUN4QkUsc0JBQXNCO2dCQUN0QkcseUJBQXlCO2dCQUN6QlQsZUFBZUQ7Z0JBQ2ZrSSxZQUFZLElBQUlGLE9BQU9DLFdBQVc7WUFDcEMsR0FDQzNJLEVBQUUsQ0FBQyxNQUFNUyxRQUNUVixNQUFNLEdBQ05FLE1BQU07WUFFVCxJQUFJVyxhQUFhO2dCQUNmLE1BQU0sSUFBSW5CLE1BQU0sQ0FBQyw2QkFBNkIsRUFBRW1CLFlBQVlsQixPQUFPLENBQUMsQ0FBQztZQUN2RTtZQUVBLE9BQU9zSjtRQUNULEVBQUUsT0FBT3pKLE9BQU87WUFDZFcsUUFBUVgsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0MsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhd0osZUFBcUM7UUFDaEQsSUFBSTtZQUNGLE1BQU0sRUFBRXpKLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUwsd0RBQWFBLENBQ3hDVyxJQUFJLENBQUNWLGlEQUFNQSxDQUFDVyxLQUFLLEVBQ2pCQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLGFBQWEsY0FDaEJDLE1BQU07WUFFVCxJQUFJVixPQUFPO2dCQUNULElBQUlBLE1BQU1LLElBQUksS0FBSyxZQUFZO29CQUM3QixPQUFPLEtBQUssc0JBQXNCOztnQkFDcEM7Z0JBQ0EsTUFBTSxJQUFJSCxNQUFNLENBQUMsMEJBQTBCLEVBQUVGLE1BQU1HLE9BQU8sQ0FBQyxDQUFDO1lBQzlEO1lBRUEsT0FBT0o7UUFDVCxFQUFFLE9BQU9DLE9BQU87WUFDZFcsUUFBUVgsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsTUFBTUE7UUFDUjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9va2RvaS1tYXJrZXRwbGFjZS8uL3NyYy9saWIvc2VydmljZXMvcmVmZXJyYWxTeXN0ZW0udHM/ZWZiZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdXBhYmFzZSwgc3VwYWJhc2VBZG1pbiwgVEFCTEVTIH0gZnJvbSAnQC9saWIvc3VwYWJhc2UnXG5pbXBvcnQgeyBVc2VyLCBSZWZlcnJhbEhpZXJhcmNoeSwgQ29tbWlzc2lvblRyYW5zYWN0aW9uLCBSZWZlcnJhbFBsYWNlbWVudCwgWm9uYWxNYW5hZ2VyLCBSZWdpb25hbFNhbGVzTWFuYWdlciB9IGZyb20gJ0AvdHlwZXMnXG5cbmV4cG9ydCBpbnRlcmZhY2UgUmVmZXJyYWxTdGF0cyB7XG4gIGRpcmVjdFJlZmVycmFsczogbnVtYmVyXG4gIHRvdGFsRG93bmxpbmU6IG51bWJlclxuICB0b3RhbENvbW1pc3Npb25FYXJuZWQ6IG51bWJlclxuICBjdXJyZW50TGV2ZWw6IG51bWJlclxuICB0b3RhbFJlZmVycmFsczogbnVtYmVyXG4gIHJlZmVycmFsQ29kZT86IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFJlZmVycmFsVHJlZU5vZGUge1xuICB1c2VyOiBVc2VyXG4gIGNoaWxkcmVuOiBSZWZlcnJhbFRyZWVOb2RlW11cbiAgbGV2ZWw6IG51bWJlclxuICBwb3NpdGlvbjogbnVtYmVyXG59XG5cbi8qKlxuICogUmVmZXJyYWxTeXN0ZW1TZXJ2aWNlIC0gTWFuYWdlcyB0aGUgbXVsdGktbGV2ZWwgcmVmZXJyYWwgYW5kIGNvbW1pc3Npb24gc3lzdGVtXG4gKi9cbmV4cG9ydCBjbGFzcyBSZWZlcnJhbFN5c3RlbVNlcnZpY2Uge1xuICAvKipcbiAgICogR2VuZXJhdGUgYSB1bmlxdWUgcmVmZXJyYWwgY29kZVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdlbmVyYXRlUmVmZXJyYWxDb2RlKCk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UucnBjKCdnZW5lcmF0ZV9yZWZlcnJhbF9jb2RlJylcbiAgICBcbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdlbmVyYXRlIHJlZmVycmFsIGNvZGU6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cbiAgICBcbiAgICByZXR1cm4gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIFZhbGlkYXRlIHJlZmVycmFsIGNvZGUgYW5kIGdldCByZWZlcnJlciBpbmZvcm1hdGlvblxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHZhbGlkYXRlUmVmZXJyYWxDb2RlKGNvZGU6IHN0cmluZyk6IFByb21pc2U8VXNlciB8IG51bGw+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlVTRVJTKVxuICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgLmVxKCdyZWZlcnJhbF9jb2RlJywgY29kZSlcbiAgICAgICAgLmVxKCdpc19yZWZlcnJhbF9hY3RpdmUnLCB0cnVlKVxuICAgICAgICAuc2luZ2xlKClcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIGlmIChlcnJvci5jb2RlID09PSAnUEdSU1QxMTYnKSB7XG4gICAgICAgICAgcmV0dXJuIG51bGwgLy8gQ29kZSBub3QgZm91bmRcbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byB2YWxpZGF0ZSByZWZlcnJhbCBjb2RlOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGRhdGFcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdmFsaWRhdGluZyByZWZlcnJhbCBjb2RlOicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogUGxhY2UgdXNlciBpbiByZWZlcnJhbCBoaWVyYXJjaHlcbiAgICovXG4gIHN0YXRpYyBhc3luYyBwbGFjZVVzZXJJbkhpZXJhcmNoeShuZXdVc2VySWQ6IHN0cmluZywgcmVmZXJyZXJJZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLnJwYygncGxhY2VfdXNlcl9pbl9oaWVyYXJjaHknLCB7XG4gICAgICAgIG5ld191c2VyX2lkOiBuZXdVc2VySWQsXG4gICAgICAgIHJlZmVycmVyX2lkOiByZWZlcnJlcklkXG4gICAgICB9KVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gcGxhY2UgdXNlciBpbiBoaWVyYXJjaHk6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwbGFjaW5nIHVzZXIgaW4gaGllcmFyY2h5OicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IHVzZXIncyByZWZlcnJhbCBzdGF0aXN0aWNzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0VXNlclJlZmVycmFsU3RhdHModXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPFJlZmVycmFsU3RhdHM+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlVTRVJTKVxuICAgICAgICAuc2VsZWN0KCdkaXJlY3RfcmVmZXJyYWxzX2NvdW50LCB0b3RhbF9kb3dubGluZV9jb3VudCwgdG90YWxfY29tbWlzc2lvbl9lYXJuZWQsIHJlZmVycmFsX2xldmVsLCByZWZlcnJhbF9jb2RlJylcbiAgICAgICAgLmVxKCdpZCcsIHVzZXJJZClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBnZXQgcmVmZXJyYWwgc3RhdHM6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICBsZXQgcmVmZXJyYWxDb2RlID0gZGF0YS5yZWZlcnJhbF9jb2RlXG5cbiAgICAgIC8vIElmIHVzZXIgZG9lc24ndCBoYXZlIGEgcmVmZXJyYWwgY29kZSwgZ2VuZXJhdGUgb25lXG4gICAgICBpZiAoIXJlZmVycmFsQ29kZSkge1xuICAgICAgICByZWZlcnJhbENvZGUgPSBhd2FpdCB0aGlzLmdlbmVyYXRlUmVmZXJyYWxDb2RlKClcblxuICAgICAgICAvLyBVcGRhdGUgdGhlIHVzZXIgd2l0aCB0aGUgbmV3IHJlZmVycmFsIGNvZGVcbiAgICAgICAgY29uc3QgeyBlcnJvcjogdXBkYXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oVEFCTEVTLlVTRVJTKVxuICAgICAgICAgIC51cGRhdGUoeyByZWZlcnJhbF9jb2RlOiByZWZlcnJhbENvZGUgfSlcbiAgICAgICAgICAuZXEoJ2lkJywgdXNlcklkKVxuXG4gICAgICAgIGlmICh1cGRhdGVFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHJlZmVycmFsIGNvZGU6JywgdXBkYXRlRXJyb3IpXG4gICAgICAgICAgLy8gRG9uJ3QgdGhyb3cgZXJyb3IsIGp1c3QgdXNlIHRoZSBnZW5lcmF0ZWQgY29kZVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGRpcmVjdFJlZmVycmFsczogZGF0YS5kaXJlY3RfcmVmZXJyYWxzX2NvdW50IHx8IDAsXG4gICAgICAgIHRvdGFsRG93bmxpbmU6IGRhdGEudG90YWxfZG93bmxpbmVfY291bnQgfHwgMCxcbiAgICAgICAgdG90YWxDb21taXNzaW9uRWFybmVkOiBwYXJzZUZsb2F0KGRhdGEudG90YWxfY29tbWlzc2lvbl9lYXJuZWQpIHx8IDAsXG4gICAgICAgIGN1cnJlbnRMZXZlbDogZGF0YS5yZWZlcnJhbF9sZXZlbCB8fCAwLFxuICAgICAgICB0b3RhbFJlZmVycmFsczogZGF0YS5kaXJlY3RfcmVmZXJyYWxzX2NvdW50IHx8IDAsIC8vIEZvciBub3csIHNhbWUgYXMgZGlyZWN0IHJlZmVycmFsc1xuICAgICAgICByZWZlcnJhbENvZGU6IHJlZmVycmFsQ29kZVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHJlZmVycmFsIHN0YXRzOicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IHVzZXIncyBkaXJlY3QgcmVmZXJyYWxzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0RGlyZWN0UmVmZXJyYWxzKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxVc2VyW10+IHtcbiAgICB0cnkge1xuICAgICAgLy8gRmlyc3QgZ2V0IHRoZSBwbGFjZW1lbnQgcmVjb3Jkc1xuICAgICAgY29uc3QgeyBkYXRhOiBwbGFjZW1lbnRzLCBlcnJvcjogcGxhY2VtZW50RXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgICAgLmZyb20oVEFCTEVTLlJFRkVSUkFMX1BMQUNFTUVOVFMpXG4gICAgICAgIC5zZWxlY3QoJ2NoaWxkX2lkLCBwb3NpdGlvbiwgY3JlYXRlZF9hdCcpXG4gICAgICAgIC5lcSgncGFyZW50X2lkJywgdXNlcklkKVxuICAgICAgICAub3JkZXIoJ3Bvc2l0aW9uJylcblxuICAgICAgaWYgKHBsYWNlbWVudEVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCByZWZlcnJhbCBwbGFjZW1lbnRzOiAke3BsYWNlbWVudEVycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgaWYgKCFwbGFjZW1lbnRzIHx8IHBsYWNlbWVudHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHJldHVybiBbXVxuICAgICAgfVxuXG4gICAgICAvLyBHZXQgdGhlIHVzZXIgZGV0YWlscyBmb3IgZWFjaCBjaGlsZFxuICAgICAgY29uc3QgY2hpbGRJZHMgPSBwbGFjZW1lbnRzLm1hcChwID0+IHAuY2hpbGRfaWQpXG4gICAgICBjb25zdCB7IGRhdGE6IHVzZXJzLCBlcnJvcjogdXNlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAgIC5mcm9tKFRBQkxFUy5VU0VSUylcbiAgICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAgIC5pbignaWQnLCBjaGlsZElkcylcblxuICAgICAgaWYgKHVzZXJFcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBnZXQgcmVmZXJyYWwgdXNlcnM6ICR7dXNlckVycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgLy8gU29ydCB1c2VycyBieSBwbGFjZW1lbnQgcG9zaXRpb25cbiAgICAgIGNvbnN0IHNvcnRlZFVzZXJzID0gdXNlcnM/LnNvcnQoKGEsIGIpID0+IHtcbiAgICAgICAgY29uc3QgcG9zaXRpb25BID0gcGxhY2VtZW50cy5maW5kKHAgPT4gcC5jaGlsZF9pZCA9PT0gYS5pZCk/LnBvc2l0aW9uIHx8IDBcbiAgICAgICAgY29uc3QgcG9zaXRpb25CID0gcGxhY2VtZW50cy5maW5kKHAgPT4gcC5jaGlsZF9pZCA9PT0gYi5pZCk/LnBvc2l0aW9uIHx8IDBcbiAgICAgICAgcmV0dXJuIHBvc2l0aW9uQSAtIHBvc2l0aW9uQlxuICAgICAgfSkgfHwgW11cblxuICAgICAgcmV0dXJuIHNvcnRlZFVzZXJzXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgZGlyZWN0IHJlZmVycmFsczonLCBlcnJvcilcbiAgICAgIHRocm93IGVycm9yXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCB1c2VyJ3MgcmVmZXJyYWwgdHJlZSAodXAgdG8gc3BlY2lmaWVkIGRlcHRoKVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFJlZmVycmFsVHJlZSh1c2VySWQ6IHN0cmluZywgbWF4RGVwdGg6IG51bWJlciA9IDMpOiBQcm9taXNlPFJlZmVycmFsVHJlZU5vZGU+IHtcbiAgICB0cnkge1xuICAgICAgLy8gR2V0IHRoZSByb290IHVzZXJcbiAgICAgIGNvbnN0IHsgZGF0YTogcm9vdFVzZXIsIGVycm9yOiByb290RXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5VU0VSUylcbiAgICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAgIC5lcSgnaWQnLCB1c2VySWQpXG4gICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICBpZiAocm9vdEVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCByb290IHVzZXI6ICR7cm9vdEVycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgLy8gQnVpbGQgdGhlIHRyZWUgcmVjdXJzaXZlbHkgdXNpbmcgcmVmZXJyYWxfcGxhY2VtZW50cyAodHJlZSBzdHJ1Y3R1cmUpXG4gICAgICBjb25zdCBidWlsZFRyZWUgPSBhc3luYyAodXNlcjogVXNlciwgY3VycmVudERlcHRoOiBudW1iZXIpOiBQcm9taXNlPFJlZmVycmFsVHJlZU5vZGU+ID0+IHtcbiAgICAgICAgY29uc3QgY2hpbGRyZW46IFJlZmVycmFsVHJlZU5vZGVbXSA9IFtdXG5cbiAgICAgICAgaWYgKGN1cnJlbnREZXB0aCA8IG1heERlcHRoKSB7XG4gICAgICAgICAgY29uc3QgeyBkYXRhOiBwbGFjZW1lbnRzLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAgIC5mcm9tKFRBQkxFUy5SRUZFUlJBTF9QTEFDRU1FTlRTKVxuICAgICAgICAgICAgLnNlbGVjdChgXG4gICAgICAgICAgICAgIHBvc2l0aW9uLFxuICAgICAgICAgICAgICBwbGFjZW1lbnRfdHlwZSxcbiAgICAgICAgICAgICAgY2hpbGQ6Y2hpbGRfaWQoXG4gICAgICAgICAgICAgICAgKixcbiAgICAgICAgICAgICAgICBhY3RpdmVfc3Vic2NyaXB0aW9uOnVzZXJfc3Vic2NyaXB0aW9ucyFsZWZ0KFxuICAgICAgICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICAgICAgICBzdGF0dXMsXG4gICAgICAgICAgICAgICAgICBleHBpcmVzX2F0LFxuICAgICAgICAgICAgICAgICAgcGFja2FnZTpzdWJzY3JpcHRpb25fcGFja2FnZXMoXG4gICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgIHByaWNlLFxuICAgICAgICAgICAgICAgICAgICBjdXJyZW5jeVxuICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgYClcbiAgICAgICAgICAgIC5lcSgncGFyZW50X2lkJywgdXNlci5pZClcbiAgICAgICAgICAgIC5vcmRlcigncG9zaXRpb24nKVxuXG4gICAgICAgICAgaWYgKCFlcnJvciAmJiBwbGFjZW1lbnRzKSB7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IHBsYWNlbWVudCBvZiBwbGFjZW1lbnRzKSB7XG4gICAgICAgICAgICAgIGNvbnN0IGNoaWxkTm9kZSA9IGF3YWl0IGJ1aWxkVHJlZShwbGFjZW1lbnQuY2hpbGQsIGN1cnJlbnREZXB0aCArIDEpXG4gICAgICAgICAgICAgIGNoaWxkTm9kZS5wb3NpdGlvbiA9IHBsYWNlbWVudC5wb3NpdGlvblxuICAgICAgICAgICAgICBjaGlsZHJlbi5wdXNoKGNoaWxkTm9kZSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHVzZXIsXG4gICAgICAgICAgY2hpbGRyZW4sXG4gICAgICAgICAgbGV2ZWw6IGN1cnJlbnREZXB0aCxcbiAgICAgICAgICBwb3NpdGlvbjogMCAvLyBXaWxsIGJlIHNldCBieSBwYXJlbnRcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gYXdhaXQgYnVpbGRUcmVlKHJvb3RVc2VyLCAwKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHJlZmVycmFsIHRyZWU6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgdXNlcidzIGNvbW1pc3Npb24gdHJhbnNhY3Rpb25zXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0Q29tbWlzc2lvblRyYW5zYWN0aW9ucyhcbiAgICB1c2VySWQ6IHN0cmluZyxcbiAgICBwYWdlOiBudW1iZXIgPSAxLFxuICAgIGxpbWl0OiBudW1iZXIgPSAyMFxuICApOiBQcm9taXNlPHsgdHJhbnNhY3Rpb25zOiBDb21taXNzaW9uVHJhbnNhY3Rpb25bXSwgdG90YWw6IG51bWJlciB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG9mZnNldCA9IChwYWdlIC0gMSkgKiBsaW1pdFxuXG4gICAgICBjb25zdCBbZGF0YVJlc3VsdCwgY291bnRSZXN1bHRdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKFRBQkxFUy5DT01NSVNTSU9OX1RSQU5TQUNUSU9OUylcbiAgICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgICAuZXEoJ2JlbmVmaWNpYXJ5X2lkJywgdXNlcklkKVxuICAgICAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAgIC5yYW5nZShvZmZzZXQsIG9mZnNldCArIGxpbWl0IC0gMSksXG4gICAgICAgIHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oVEFCTEVTLkNPTU1JU1NJT05fVFJBTlNBQ1RJT05TKVxuICAgICAgICAgIC5zZWxlY3QoJyonLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pXG4gICAgICAgICAgLmVxKCdiZW5lZmljaWFyeV9pZCcsIHVzZXJJZClcbiAgICAgIF0pXG5cbiAgICAgIGlmIChkYXRhUmVzdWx0LmVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCBjb21taXNzaW9uIHRyYW5zYWN0aW9uczogJHtkYXRhUmVzdWx0LmVycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgaWYgKGNvdW50UmVzdWx0LmVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNvdW50IGNvbW1pc3Npb24gdHJhbnNhY3Rpb25zOiAke2NvdW50UmVzdWx0LmVycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdHJhbnNhY3Rpb25zOiBkYXRhUmVzdWx0LmRhdGEgfHwgW10sXG4gICAgICAgIHRvdGFsOiBjb3VudFJlc3VsdC5jb3VudCB8fCAwXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgY29tbWlzc2lvbiB0cmFuc2FjdGlvbnM6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgWm9uYWwgTWFuYWdlclxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNyZWF0ZVpvbmFsTWFuYWdlcihcbiAgICB1c2VySWQ6IHN0cmluZyxcbiAgICB6b25lTmFtZTogc3RyaW5nLFxuICAgIHpvbmVEZXNjcmlwdGlvbj86IHN0cmluZyxcbiAgICBhc3NpZ25lZERpc3RyaWN0czogc3RyaW5nW10gPSBbXSxcbiAgICBjcmVhdGVkQnk/OiBzdHJpbmdcbiAgKTogUHJvbWlzZTxab25hbE1hbmFnZXI+IHtcbiAgICB0cnkge1xuICAgICAgLy8gRmlyc3QgdXBkYXRlIHVzZXIgdHlwZVxuICAgICAgY29uc3QgeyBlcnJvcjogdXNlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuVVNFUlMpXG4gICAgICAgIC51cGRhdGUoeyB1c2VyX3R5cGU6ICd6b25hbF9tYW5hZ2VyJyB9KVxuICAgICAgICAuZXEoJ2lkJywgdXNlcklkKVxuXG4gICAgICBpZiAodXNlckVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHVwZGF0ZSB1c2VyIHR5cGU6ICR7dXNlckVycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIHpvbmFsIG1hbmFnZXIgcmVjb3JkXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuWk9OQUxfTUFOQUdFUlMpXG4gICAgICAgIC5pbnNlcnQoe1xuICAgICAgICAgIHVzZXJfaWQ6IHVzZXJJZCxcbiAgICAgICAgICB6b25lX25hbWU6IHpvbmVOYW1lLFxuICAgICAgICAgIHpvbmVfZGVzY3JpcHRpb246IHpvbmVEZXNjcmlwdGlvbixcbiAgICAgICAgICBhc3NpZ25lZF9kaXN0cmljdHM6IGFzc2lnbmVkRGlzdHJpY3RzLFxuICAgICAgICAgIGNyZWF0ZWRfYnk6IGNyZWF0ZWRCeVxuICAgICAgICB9KVxuICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgem9uYWwgbWFuYWdlcjogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIHJldHVybiBkYXRhXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHpvbmFsIG1hbmFnZXI6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBVcGdyYWRlIHVzZXIgdG8gUlNNXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgdXBncmFkZVRvUlNNKFxuICAgIHVzZXJJZDogc3RyaW5nLFxuICAgIHpvbmFsTWFuYWdlcklkPzogc3RyaW5nLFxuICAgIHJlZ2lvbk5hbWU/OiBzdHJpbmcsXG4gICAgdXBncmFkZWRCeT86IHN0cmluZ1xuICApOiBQcm9taXNlPFJlZ2lvbmFsU2FsZXNNYW5hZ2VyPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEZpcnN0IHVwZGF0ZSB1c2VyIHR5cGVcbiAgICAgIGNvbnN0IHsgZXJyb3I6IHVzZXJFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlVTRVJTKVxuICAgICAgICAudXBkYXRlKHsgdXNlcl90eXBlOiAncnNtJyB9KVxuICAgICAgICAuZXEoJ2lkJywgdXNlcklkKVxuXG4gICAgICBpZiAodXNlckVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHVwZGF0ZSB1c2VyIHR5cGU6ICR7dXNlckVycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIFJTTSByZWNvcmRcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5SRUdJT05BTF9TQUxFU19NQU5BR0VSUylcbiAgICAgICAgLmluc2VydCh7XG4gICAgICAgICAgdXNlcl9pZDogdXNlcklkLFxuICAgICAgICAgIHpvbmFsX21hbmFnZXJfaWQ6IHpvbmFsTWFuYWdlcklkLFxuICAgICAgICAgIHJlZ2lvbl9uYW1lOiByZWdpb25OYW1lLFxuICAgICAgICAgIHVwZ3JhZGVkX2J5OiB1cGdyYWRlZEJ5XG4gICAgICAgIH0pXG4gICAgICAgIC5zZWxlY3QoKVxuICAgICAgICAuc2luZ2xlKClcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBSU006ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gZGF0YVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGdyYWRpbmcgdG8gUlNNOicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IGFsbCBab25hbCBNYW5hZ2Vyc1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFpvbmFsTWFuYWdlcnMoKTogUHJvbWlzZTwoWm9uYWxNYW5hZ2VyICYgeyB1c2VyOiBVc2VyIH0pW10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlpPTkFMX01BTkFHRVJTKVxuICAgICAgICAuc2VsZWN0KGBcbiAgICAgICAgICAqLFxuICAgICAgICAgIHVzZXI6dXNlcl9pZCgqKVxuICAgICAgICBgKVxuICAgICAgICAub3JkZXIoJ2NyZWF0ZWRfYXQnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCB6b25hbCBtYW5hZ2VyczogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIHJldHVybiBkYXRhIHx8IFtdXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgem9uYWwgbWFuYWdlcnM6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYWxsIFJlZ2lvbmFsIFNhbGVzIE1hbmFnZXJzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0UmVnaW9uYWxTYWxlc01hbmFnZXJzKCk6IFByb21pc2U8KFJlZ2lvbmFsU2FsZXNNYW5hZ2VyICYgeyB1c2VyOiBVc2VyLCB6b25hbF9tYW5hZ2VyPzogWm9uYWxNYW5hZ2VyIH0pW10+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlJFR0lPTkFMX1NBTEVTX01BTkFHRVJTKVxuICAgICAgICAuc2VsZWN0KGBcbiAgICAgICAgICAqLFxuICAgICAgICAgIHVzZXI6dXNlcl9pZCgqKSxcbiAgICAgICAgICB6b25hbF9tYW5hZ2VyOnpvbmFsX21hbmFnZXJfaWQoKilcbiAgICAgICAgYClcbiAgICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBnZXQgcmVnaW9uYWwgc2FsZXMgbWFuYWdlcnM6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gZGF0YSB8fCBbXVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHJlZ2lvbmFsIHNhbGVzIG1hbmFnZXJzOicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2VhcmNoIHVzZXJzIGZvciBaTS9SU00gY3JlYXRpb25cbiAgICovXG4gIHN0YXRpYyBhc3luYyBzZWFyY2hVc2VycyhzZWFyY2hUZXJtOiBzdHJpbmcsIGxpbWl0OiBudW1iZXIgPSAyMCk6IFByb21pc2U8VXNlcltdPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKFRBQkxFUy5VU0VSUylcbiAgICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAgIC5vcihgZnVsbF9uYW1lLmlsaWtlLiUke3NlYXJjaFRlcm19JSxlbWFpbC5pbGlrZS4lJHtzZWFyY2hUZXJtfSVgKVxuICAgICAgICAuZXEoJ3VzZXJfdHlwZScsICd1c2VyJykgLy8gT25seSByZWd1bGFyIHVzZXJzIGNhbiBiZSB1cGdyYWRlZFxuICAgICAgICAubGltaXQobGltaXQpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBzZWFyY2ggdXNlcnM6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gZGF0YSB8fCBbXVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZWFyY2hpbmcgdXNlcnM6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgcmVmZXJyYWwgc3lzdGVtIHN0YXRpc3RpY3NcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRSZWZlcnJhbFN5c3RlbVN0YXRzKCk6IFByb21pc2U8e1xuICAgIHRvdGFsVXNlcnM6IG51bWJlclxuICAgIHpvbmFsTWFuYWdlcnM6IG51bWJlclxuICAgIHJlZ2lvbmFsTWFuYWdlcnM6IG51bWJlclxuICAgIHRvdGFsUmVmZXJyYWxzOiBudW1iZXJcbiAgICBhY3RpdmVSZWZlcnJhbENvZGVzOiBudW1iZXJcbiAgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBbXG4gICAgICAgIHRvdGFsVXNlcnNSZXN1bHQsXG4gICAgICAgIHpvbmFsTWFuYWdlcnNSZXN1bHQsXG4gICAgICAgIHJlZ2lvbmFsTWFuYWdlcnNSZXN1bHQsXG4gICAgICAgIHRvdGFsUmVmZXJyYWxzUmVzdWx0LFxuICAgICAgICBhY3RpdmVDb2Rlc1Jlc3VsdFxuICAgICAgXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgc3VwYWJhc2UuZnJvbShUQUJMRVMuVVNFUlMpLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSksXG4gICAgICAgIHN1cGFiYXNlLmZyb20oVEFCTEVTLlpPTkFMX01BTkFHRVJTKS5zZWxlY3QoJyonLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pLmVxKCdpc19hY3RpdmUnLCB0cnVlKSxcbiAgICAgICAgc3VwYWJhc2UuZnJvbShUQUJMRVMuUkVHSU9OQUxfU0FMRVNfTUFOQUdFUlMpLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSkuZXEoJ2lzX2FjdGl2ZScsIHRydWUpLFxuICAgICAgICBzdXBhYmFzZS5mcm9tKFRBQkxFUy5SRUZFUlJBTF9QTEFDRU1FTlRTKS5zZWxlY3QoJyonLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pLFxuICAgICAgICBzdXBhYmFzZS5mcm9tKFRBQkxFUy5VU0VSUykuc2VsZWN0KCcqJywgeyBjb3VudDogJ2V4YWN0JywgaGVhZDogdHJ1ZSB9KS5ub3QoJ3JlZmVycmFsX2NvZGUnLCAnaXMnLCBudWxsKVxuICAgICAgXSlcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdG90YWxVc2VyczogdG90YWxVc2Vyc1Jlc3VsdC5jb3VudCB8fCAwLFxuICAgICAgICB6b25hbE1hbmFnZXJzOiB6b25hbE1hbmFnZXJzUmVzdWx0LmNvdW50IHx8IDAsXG4gICAgICAgIHJlZ2lvbmFsTWFuYWdlcnM6IHJlZ2lvbmFsTWFuYWdlcnNSZXN1bHQuY291bnQgfHwgMCxcbiAgICAgICAgdG90YWxSZWZlcnJhbHM6IHRvdGFsUmVmZXJyYWxzUmVzdWx0LmNvdW50IHx8IDAsXG4gICAgICAgIGFjdGl2ZVJlZmVycmFsQ29kZXM6IGFjdGl2ZUNvZGVzUmVzdWx0LmNvdW50IHx8IDBcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyByZWZlcnJhbCBzeXN0ZW0gc3RhdHM6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBEZWFjdGl2YXRlIFpvbmFsIE1hbmFnZXJcbiAgICovXG4gIHN0YXRpYyBhc3luYyBkZWFjdGl2YXRlWm9uYWxNYW5hZ2VyKHptSWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbShUQUJMRVMuWk9OQUxfTUFOQUdFUlMpXG4gICAgICAgIC51cGRhdGUoeyBpc19hY3RpdmU6IGZhbHNlIH0pXG4gICAgICAgIC5lcSgnaWQnLCB6bUlkKVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZGVhY3RpdmF0ZSB6b25hbCBtYW5hZ2VyOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVhY3RpdmF0aW5nIHpvbmFsIG1hbmFnZXI6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBEZWFjdGl2YXRlIFJlZ2lvbmFsIFNhbGVzIE1hbmFnZXJcbiAgICovXG4gIHN0YXRpYyBhc3luYyBkZWFjdGl2YXRlUlNNKHJzbUlkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oVEFCTEVTLlJFR0lPTkFMX1NBTEVTX01BTkFHRVJTKVxuICAgICAgICAudXBkYXRlKHsgaXNfYWN0aXZlOiBmYWxzZSB9KVxuICAgICAgICAuZXEoJ2lkJywgcnNtSWQpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBkZWFjdGl2YXRlIFJTTTogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlYWN0aXZhdGluZyBSU006JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgb3IgZ2V0IE9LRE9JIEhlYWQgdXNlclxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGNyZWF0ZU9LRE9JSGVhZChcbiAgICBlbWFpbDogc3RyaW5nID0gJ29rZG9pLmhlYWRAb2tkb2kuY29tJyxcbiAgICBmdWxsTmFtZTogc3RyaW5nID0gJ09LRE9JIEhlYWQnLFxuICAgIHBob25lPzogc3RyaW5nXG4gICk6IFByb21pc2U8VXNlcj4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBDaGVjayBpZiBPS0RPSSBIZWFkIGFscmVhZHkgZXhpc3RzIHVzaW5nIGFkbWluIGNsaWVudFxuICAgICAgY29uc3QgeyBkYXRhOiBleGlzdGluZ0hlYWQsIGVycm9yOiBjaGVja0Vycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAgIC5mcm9tKFRBQkxFUy5VU0VSUylcbiAgICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAgIC5lcSgndXNlcl90eXBlJywgJ29rZG9pX2hlYWQnKVxuICAgICAgICAuc2luZ2xlKClcblxuICAgICAgaWYgKGV4aXN0aW5nSGVhZCAmJiAhY2hlY2tFcnJvcikge1xuICAgICAgICByZXR1cm4gZXhpc3RpbmdIZWFkXG4gICAgICB9XG5cbiAgICAgIC8vIEdlbmVyYXRlIHJlZmVycmFsIGNvZGUgZmlyc3RcbiAgICAgIGNvbnN0IHJlZmVycmFsQ29kZSA9IGF3YWl0IHRoaXMuZ2VuZXJhdGVSZWZlcnJhbENvZGUoKVxuXG4gICAgICAvLyBDcmVhdGUgYXV0aCB1c2VyIGZpcnN0IHVzaW5nIGFkbWluIGF1dGggQVBJXG4gICAgICBjb25zdCB7IGRhdGE6IGF1dGhVc2VyLCBlcnJvcjogYXV0aEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluLmF1dGguYWRtaW4uY3JlYXRlVXNlcih7XG4gICAgICAgIGVtYWlsLFxuICAgICAgICBwYXNzd29yZDogY3J5cHRvLnJhbmRvbVVVSUQoKSwgLy8gR2VuZXJhdGUgYSByYW5kb20gcGFzc3dvcmRcbiAgICAgICAgZW1haWxfY29uZmlybTogdHJ1ZSwgLy8gQXV0by1jb25maXJtIGVtYWlsXG4gICAgICAgIHVzZXJfbWV0YWRhdGE6IHtcbiAgICAgICAgICBmdWxsX25hbWU6IGZ1bGxOYW1lLFxuICAgICAgICAgIHBob25lOiBwaG9uZSB8fCBudWxsXG4gICAgICAgIH1cbiAgICAgIH0pXG5cbiAgICAgIGlmIChhdXRoRXJyb3IgfHwgIWF1dGhVc2VyLnVzZXIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gY3JlYXRlIGF1dGggdXNlcjogJHthdXRoRXJyb3I/Lm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIE9LRE9JIEhlYWQgdXNlciBpbiBwdWJsaWMudXNlcnMgdGFibGUgdXNpbmcgdGhlIGF1dGggdXNlciBJRFxuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgICAuZnJvbShUQUJMRVMuVVNFUlMpXG4gICAgICAgIC5pbnNlcnQoe1xuICAgICAgICAgIGlkOiBhdXRoVXNlci51c2VyLmlkLCAvLyBVc2UgdGhlIGF1dGggdXNlciBJRFxuICAgICAgICAgIGVtYWlsLFxuICAgICAgICAgIGZ1bGxfbmFtZTogZnVsbE5hbWUsXG4gICAgICAgICAgcGhvbmUsXG4gICAgICAgICAgdXNlcl90eXBlOiAnb2tkb2lfaGVhZCcsXG4gICAgICAgICAgcm9sZTogJ2FkbWluJyxcbiAgICAgICAgICBpc192ZXJpZmllZDogdHJ1ZSxcbiAgICAgICAgICBpc19yZWZlcnJhbF9hY3RpdmU6IHRydWUsXG4gICAgICAgICAgcmVmZXJyYWxfbGV2ZWw6IDAsXG4gICAgICAgICAgcmVmZXJyYWxfcGF0aDogJycsXG4gICAgICAgICAgZGlyZWN0X3JlZmVycmFsc19jb3VudDogMCxcbiAgICAgICAgICB0b3RhbF9kb3dubGluZV9jb3VudDogMCxcbiAgICAgICAgICB0b3RhbF9jb21taXNzaW9uX2Vhcm5lZDogMCxcbiAgICAgICAgICByZWZlcnJhbF9jb2RlOiByZWZlcnJhbENvZGUsXG4gICAgICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9KVxuICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAvLyBJZiB1c2VyIGNyZWF0aW9uIGZhaWxzLCBjbGVhbiB1cCB0aGUgYXV0aCB1c2VyXG4gICAgICAgIGF3YWl0IHN1cGFiYXNlQWRtaW4uYXV0aC5hZG1pbi5kZWxldGVVc2VyKGF1dGhVc2VyLnVzZXIuaWQpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBPS0RPSSBIZWFkOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGRhdGFcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgT0tET0kgSGVhZDonLCBlcnJvcilcbiAgICAgIHRocm93IGVycm9yXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEFzc2lnbiBPS0RPSSBIZWFkIHJvbGUgdG8gZXhpc3RpbmcgdXNlclxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGFzc2lnbk9LRE9JSGVhZFRvVXNlcih1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8VXNlcj4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBDaGVjayBpZiBPS0RPSSBIZWFkIGFscmVhZHkgZXhpc3RzXG4gICAgICBjb25zdCBleGlzdGluZ0hlYWQgPSBhd2FpdCB0aGlzLmdldE9LRE9JSGVhZCgpXG4gICAgICBpZiAoZXhpc3RpbmdIZWFkKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignT0tET0kgSGVhZCBhbHJlYWR5IGV4aXN0cy4gT25seSBvbmUgT0tET0kgSGVhZCBpcyBhbGxvd2VkLicpXG4gICAgICB9XG5cbiAgICAgIC8vIEdldCB0aGUgdXNlciB0byBiZSBhc3NpZ25lZFxuICAgICAgY29uc3QgeyBkYXRhOiB1c2VyLCBlcnJvcjogdXNlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAgIC5mcm9tKFRBQkxFUy5VU0VSUylcbiAgICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAgIC5lcSgnaWQnLCB1c2VySWQpXG4gICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICBpZiAodXNlckVycm9yIHx8ICF1c2VyKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgZm91bmQnKVxuICAgICAgfVxuXG4gICAgICAvLyBHZW5lcmF0ZSByZWZlcnJhbCBjb2RlIGlmIHVzZXIgZG9lc24ndCBoYXZlIG9uZVxuICAgICAgbGV0IHJlZmVycmFsQ29kZSA9IHVzZXIucmVmZXJyYWxfY29kZVxuICAgICAgaWYgKCFyZWZlcnJhbENvZGUpIHtcbiAgICAgICAgcmVmZXJyYWxDb2RlID0gYXdhaXQgdGhpcy5nZW5lcmF0ZVJlZmVycmFsQ29kZSgpXG4gICAgICB9XG5cbiAgICAgIC8vIFVwZGF0ZSB1c2VyIHRvIE9LRE9JIEhlYWRcbiAgICAgIGNvbnN0IHsgZGF0YTogdXBkYXRlZFVzZXIsIGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgICAuZnJvbShUQUJMRVMuVVNFUlMpXG4gICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgIHVzZXJfdHlwZTogJ29rZG9pX2hlYWQnLFxuICAgICAgICAgIHJvbGU6ICdhZG1pbicsXG4gICAgICAgICAgaXNfdmVyaWZpZWQ6IHRydWUsXG4gICAgICAgICAgaXNfcmVmZXJyYWxfYWN0aXZlOiB0cnVlLFxuICAgICAgICAgIHJlZmVycmFsX2xldmVsOiAwLFxuICAgICAgICAgIHJlZmVycmFsX3BhdGg6ICcnLFxuICAgICAgICAgIGRpcmVjdF9yZWZlcnJhbHNfY291bnQ6IDAsXG4gICAgICAgICAgdG90YWxfZG93bmxpbmVfY291bnQ6IDAsXG4gICAgICAgICAgdG90YWxfY29tbWlzc2lvbl9lYXJuZWQ6IDAsXG4gICAgICAgICAgcmVmZXJyYWxfY29kZTogcmVmZXJyYWxDb2RlLFxuICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9KVxuICAgICAgICAuZXEoJ2lkJywgdXNlcklkKVxuICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgLnNpbmdsZSgpXG5cbiAgICAgIGlmICh1cGRhdGVFcnJvcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBhc3NpZ24gT0tET0kgSGVhZDogJHt1cGRhdGVFcnJvci5tZXNzYWdlfWApXG4gICAgICB9XG5cbiAgICAgIHJldHVybiB1cGRhdGVkVXNlclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhc3NpZ25pbmcgT0tET0kgSGVhZDonLCBlcnJvcilcbiAgICAgIHRocm93IGVycm9yXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBPS0RPSSBIZWFkIHVzZXJcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRPS0RPSUhlYWQoKTogUHJvbWlzZTxVc2VyIHwgbnVsbD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAgIC5mcm9tKFRBQkxFUy5VU0VSUylcbiAgICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAgIC5lcSgndXNlcl90eXBlJywgJ29rZG9pX2hlYWQnKVxuICAgICAgICAuc2luZ2xlKClcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIGlmIChlcnJvci5jb2RlID09PSAnUEdSU1QxMTYnKSB7XG4gICAgICAgICAgcmV0dXJuIG51bGwgLy8gTm8gT0tET0kgSGVhZCBmb3VuZFxuICAgICAgICB9XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGdldCBPS0RPSSBIZWFkOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGRhdGFcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBPS0RPSSBIZWFkOicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJzdXBhYmFzZSIsInN1cGFiYXNlQWRtaW4iLCJUQUJMRVMiLCJSZWZlcnJhbFN5c3RlbVNlcnZpY2UiLCJnZW5lcmF0ZVJlZmVycmFsQ29kZSIsImRhdGEiLCJlcnJvciIsInJwYyIsIkVycm9yIiwibWVzc2FnZSIsInZhbGlkYXRlUmVmZXJyYWxDb2RlIiwiY29kZSIsImZyb20iLCJVU0VSUyIsInNlbGVjdCIsImVxIiwic2luZ2xlIiwiY29uc29sZSIsInBsYWNlVXNlckluSGllcmFyY2h5IiwibmV3VXNlcklkIiwicmVmZXJyZXJJZCIsIm5ld191c2VyX2lkIiwicmVmZXJyZXJfaWQiLCJnZXRVc2VyUmVmZXJyYWxTdGF0cyIsInVzZXJJZCIsInJlZmVycmFsQ29kZSIsInJlZmVycmFsX2NvZGUiLCJ1cGRhdGVFcnJvciIsInVwZGF0ZSIsImRpcmVjdFJlZmVycmFscyIsImRpcmVjdF9yZWZlcnJhbHNfY291bnQiLCJ0b3RhbERvd25saW5lIiwidG90YWxfZG93bmxpbmVfY291bnQiLCJ0b3RhbENvbW1pc3Npb25FYXJuZWQiLCJwYXJzZUZsb2F0IiwidG90YWxfY29tbWlzc2lvbl9lYXJuZWQiLCJjdXJyZW50TGV2ZWwiLCJyZWZlcnJhbF9sZXZlbCIsInRvdGFsUmVmZXJyYWxzIiwiZ2V0RGlyZWN0UmVmZXJyYWxzIiwicGxhY2VtZW50cyIsInBsYWNlbWVudEVycm9yIiwiUkVGRVJSQUxfUExBQ0VNRU5UUyIsIm9yZGVyIiwibGVuZ3RoIiwiY2hpbGRJZHMiLCJtYXAiLCJwIiwiY2hpbGRfaWQiLCJ1c2VycyIsInVzZXJFcnJvciIsImluIiwic29ydGVkVXNlcnMiLCJzb3J0IiwiYSIsImIiLCJwb3NpdGlvbkEiLCJmaW5kIiwiaWQiLCJwb3NpdGlvbiIsInBvc2l0aW9uQiIsImdldFJlZmVycmFsVHJlZSIsIm1heERlcHRoIiwicm9vdFVzZXIiLCJyb290RXJyb3IiLCJidWlsZFRyZWUiLCJ1c2VyIiwiY3VycmVudERlcHRoIiwiY2hpbGRyZW4iLCJwbGFjZW1lbnQiLCJjaGlsZE5vZGUiLCJjaGlsZCIsInB1c2giLCJsZXZlbCIsImdldENvbW1pc3Npb25UcmFuc2FjdGlvbnMiLCJwYWdlIiwibGltaXQiLCJvZmZzZXQiLCJkYXRhUmVzdWx0IiwiY291bnRSZXN1bHQiLCJQcm9taXNlIiwiYWxsIiwiQ09NTUlTU0lPTl9UUkFOU0FDVElPTlMiLCJhc2NlbmRpbmciLCJyYW5nZSIsImNvdW50IiwiaGVhZCIsInRyYW5zYWN0aW9ucyIsInRvdGFsIiwiY3JlYXRlWm9uYWxNYW5hZ2VyIiwiem9uZU5hbWUiLCJ6b25lRGVzY3JpcHRpb24iLCJhc3NpZ25lZERpc3RyaWN0cyIsImNyZWF0ZWRCeSIsInVzZXJfdHlwZSIsIlpPTkFMX01BTkFHRVJTIiwiaW5zZXJ0IiwidXNlcl9pZCIsInpvbmVfbmFtZSIsInpvbmVfZGVzY3JpcHRpb24iLCJhc3NpZ25lZF9kaXN0cmljdHMiLCJjcmVhdGVkX2J5IiwidXBncmFkZVRvUlNNIiwiem9uYWxNYW5hZ2VySWQiLCJyZWdpb25OYW1lIiwidXBncmFkZWRCeSIsIlJFR0lPTkFMX1NBTEVTX01BTkFHRVJTIiwiem9uYWxfbWFuYWdlcl9pZCIsInJlZ2lvbl9uYW1lIiwidXBncmFkZWRfYnkiLCJnZXRab25hbE1hbmFnZXJzIiwiZ2V0UmVnaW9uYWxTYWxlc01hbmFnZXJzIiwic2VhcmNoVXNlcnMiLCJzZWFyY2hUZXJtIiwib3IiLCJnZXRSZWZlcnJhbFN5c3RlbVN0YXRzIiwidG90YWxVc2Vyc1Jlc3VsdCIsInpvbmFsTWFuYWdlcnNSZXN1bHQiLCJyZWdpb25hbE1hbmFnZXJzUmVzdWx0IiwidG90YWxSZWZlcnJhbHNSZXN1bHQiLCJhY3RpdmVDb2Rlc1Jlc3VsdCIsIm5vdCIsInRvdGFsVXNlcnMiLCJ6b25hbE1hbmFnZXJzIiwicmVnaW9uYWxNYW5hZ2VycyIsImFjdGl2ZVJlZmVycmFsQ29kZXMiLCJkZWFjdGl2YXRlWm9uYWxNYW5hZ2VyIiwiem1JZCIsImlzX2FjdGl2ZSIsImRlYWN0aXZhdGVSU00iLCJyc21JZCIsImNyZWF0ZU9LRE9JSGVhZCIsImVtYWlsIiwiZnVsbE5hbWUiLCJwaG9uZSIsImV4aXN0aW5nSGVhZCIsImNoZWNrRXJyb3IiLCJhdXRoVXNlciIsImF1dGhFcnJvciIsImF1dGgiLCJhZG1pbiIsImNyZWF0ZVVzZXIiLCJwYXNzd29yZCIsImNyeXB0byIsInJhbmRvbVVVSUQiLCJlbWFpbF9jb25maXJtIiwidXNlcl9tZXRhZGF0YSIsImZ1bGxfbmFtZSIsInJvbGUiLCJpc192ZXJpZmllZCIsImlzX3JlZmVycmFsX2FjdGl2ZSIsInJlZmVycmFsX3BhdGgiLCJjcmVhdGVkX2F0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidXBkYXRlZF9hdCIsImRlbGV0ZVVzZXIiLCJhc3NpZ25PS0RPSUhlYWRUb1VzZXIiLCJnZXRPS0RPSUhlYWQiLCJ1cGRhdGVkVXNlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/referralSystem.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();