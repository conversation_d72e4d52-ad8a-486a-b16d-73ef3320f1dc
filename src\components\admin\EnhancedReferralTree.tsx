'use client'

import { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import {
  ChevronDown,
  ChevronRight,
  Crown,
  Users,
  Network,
  User as UserIcon,
  Shield,
  Phone,
  Mail,
  Calendar,
  MapPin,
  Star,
  Award,
  TrendingUp,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  UserCheck,
  UserX,
  Package,
  Activity,
  Move
} from 'lucide-react'
import { User } from '@/types'
import { useTreeNavigation } from '@/hooks/useTreeNavigation'
import { useTreeSearch } from '@/hooks/useTreeSearch'
import { useTreeExport } from '@/hooks/useTreeExport'
import ReferralTreeToolbar from './ReferralTreeToolbar'

export interface EnhancedNetworkTreeNode {
  user: User & {
    active_subscription?: {
      id: string
      status: string
      expires_at: string
      package: {
        name: string
        price: number
        currency: string
      }
    }[]
  }
  children: EnhancedNetworkTreeNode[]
  level: number
  position: number
  placement_type?: 'direct' | 'spillover'
  stats?: {
    directReferrals: number
    totalDownline: number
    totalCommission: number
  }
}

interface EnhancedReferralTreeProps {
  rootNode: EnhancedNetworkTreeNode | null
  loading?: boolean
  onNodeClick?: (user: User) => void
  className?: string
}

interface TreeNodeProps {
  node: EnhancedNetworkTreeNode
  onNodeClick?: (user: User) => void
  isExpanded: boolean
  onToggleExpand: (nodeId: string) => void
  parentId?: string
  expandedNodes: Set<string>
  selectedNodeId?: string | null
  searchTerm?: string
}

function TreeNode({ node, onNodeClick, isExpanded, onToggleExpand, parentId, expandedNodes, selectedNodeId, searchTerm }: TreeNodeProps) {
  const [isHovered, setIsHovered] = useState(false)
  const hasChildren = node.children && node.children.length > 0
  const isSelected = selectedNodeId === node.user.id
  const isSearchMatch = searchTerm && (
    node.user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    node.user.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getUserTypeIcon = (userType: string) => {
    switch (userType) {
      case 'okdoi_head':
        return <Crown className="h-3 w-3 text-yellow-400" />
      case 'zonal_manager':
        return <Shield className="h-3 w-3 text-blue-400" />
      case 'rsm':
        return <Network className="h-3 w-3 text-green-400" />
      default:
        return <UserIcon className="h-3 w-3 text-gray-400" />
    }
  }

  const getUserTypeColor = (userType: string) => {
    switch (userType) {
      case 'okdoi_head':
        return 'from-yellow-500 to-orange-500'
      case 'zonal_manager':
        return 'from-blue-500 to-indigo-500'
      case 'rsm':
        return 'from-green-500 to-emerald-500'
      default:
        return 'from-gray-500 to-slate-500'
    }
  }

  const getReferralTypeInfo = () => {
    if (!parentId) return null

    return node.placement_type === 'direct' ? (
      <div className="flex items-center space-x-1 text-green-600 bg-green-50 px-1 py-0.5 rounded text-xs">
        <UserCheck className="h-2 w-2" />
        <span className="text-xs">Direct</span>
      </div>
    ) : (
      <div className="flex items-center space-x-1 text-blue-600 bg-blue-50 px-1 py-0.5 rounded text-xs">
        <Users className="h-2 w-2" />
        <span className="text-xs">Spillover</span>
      </div>
    )
  }

  const getSubscriptionInfo = () => {
    const subscription = node.user.active_subscription?.[0]
    if (!subscription) {
      return (
        <div className="flex items-center space-x-1 text-red-600 bg-red-50 px-1 py-0.5 rounded text-xs">
          <XCircle className="h-2 w-2" />
          <span className="text-xs">No Package</span>
        </div>
      )
    }

    const isActive = subscription.status === 'active' && new Date(subscription.expires_at) > new Date()

    return (
      <div className={`flex items-center space-x-1 px-1 py-0.5 rounded text-xs ${
        isActive
          ? 'text-green-600 bg-green-50'
          : 'text-orange-600 bg-orange-50'
      }`}>
        <Package className="h-2 w-2" />
        <span className="text-xs">{subscription.package.name}</span>
        {isActive ? (
          <CheckCircle className="h-2 w-2" />
        ) : (
          <Clock className="h-2 w-2" />
        )}
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="flex flex-col items-center relative">


      {/* User Node */}
      <div className="relative z-10">
        <div
          data-node-card="true"
          className={`
            relative bg-white rounded-md shadow-lg border-2 transition-all duration-300 cursor-pointer w-52 select-none
            backdrop-blur-sm bg-white/95
            ${isSelected ? 'border-blue-500 shadow-blue-200 shadow-xl' :
              isSearchMatch ? 'border-yellow-400 shadow-yellow-200 shadow-lg bg-yellow-50' :
              'border-gray-100'}
            ${isHovered ? 'shadow-2xl scale-105 -translate-y-1' : ''}
          `}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={() => onNodeClick?.(node.user)}
          style={{
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none'
          }}
        >
          {/* Premium Header */}
          <div className={`bg-gradient-to-br ${getUserTypeColor(node.user.user_type || 'user')} p-3 rounded-t-md text-white shadow-inner`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getUserTypeIcon(node.user.user_type || 'user')}
                <div className="min-w-0 flex-1">
                  <h3 className="font-medium text-sm truncate">{node.user.full_name || 'Unknown User'}</h3>
                  <p className="text-xs opacity-90 capitalize truncate">
                    {node.user.user_type?.replace('_', ' ') || 'User'}
                  </p>
                </div>
              </div>

              {/* Expand/Collapse Button */}
              {hasChildren && (
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onToggleExpand(node.user.id)
                  }}
                  className="p-1 rounded bg-white/20 hover:bg-white/30 transition-colors flex-shrink-0"
                >
                  {isExpanded ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronRight className="h-3 w-3" />
                  )}
                </button>
              )}
            </div>
          </div>

          {/* Compact Content */}
          <div className="p-2 space-y-2">
            {/* Status Badges */}
            <div className="flex flex-wrap gap-1">
              {getReferralTypeInfo()}
              {getSubscriptionInfo()}
            </div>

            {/* Contact Information - Compact */}
            <div className="space-y-1 text-xs">
              {node.user.email && (
                <div className="flex items-center space-x-1 text-gray-600">
                  <Mail className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate">{node.user.email}</span>
                </div>
              )}

              {node.user.phone && (
                <div className="flex items-center space-x-1 text-gray-600">
                  <Phone className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate">{node.user.phone}</span>
                </div>
              )}
            </div>

            {/* Compact Stats */}
            <div className="grid grid-cols-3 gap-1 pt-1 border-t border-gray-100">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 text-blue-600">
                  <Users className="h-2 w-2" />
                  <span className="text-xs font-medium">Dir</span>
                </div>
                <div className="text-sm font-bold text-gray-800">
                  {node.user.direct_referrals_count || 0}
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 text-green-600">
                  <TrendingUp className="h-2 w-2" />
                  <span className="text-xs font-medium">Tot</span>
                </div>
                <div className="text-sm font-bold text-gray-800">
                  {node.user.total_downline_count || 0}
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 text-purple-600">
                  <DollarSign className="h-2 w-2" />
                  <span className="text-xs font-medium">Com</span>
                </div>
                <div className="text-sm font-bold text-gray-800">
                  {(node.user.total_commission_earned || 0) > 999
                    ? `${Math.floor((node.user.total_commission_earned || 0) / 1000)}k`
                    : (node.user.total_commission_earned || 0)}
                </div>
              </div>
            </div>

            {/* Join Date - Compact */}
            <div className="flex items-center justify-center space-x-1 text-xs text-gray-500 pt-1 border-t border-gray-100">
              <Calendar className="h-2 w-2" />
              <span>{formatDate(node.user.created_at)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Children with connecting lines */}
      {hasChildren && isExpanded && (
        <div className="relative mt-8">
          {/* Vertical line down from parent */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-8 bg-gray-400 -top-8"></div>

          {/* Horizontal connector line (only if multiple children) */}
          {node.children.length > 1 && (
            <div
              className="absolute h-0.5 bg-gray-400 -top-4"
              style={{
                left: `calc(50% - ${((node.children.length - 1) * (208 + 64)) / 2}px)`,
                width: `${(node.children.length - 1) * (208 + 64)}px`
              }}
            ></div>
          )}

          {/* Children container */}
          <div className="flex justify-center gap-16 relative">
            {node.children.map((child, index) => (
              <div key={child.user.id} className="relative">
                {/* Vertical line up to horizontal connector */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-4 bg-gray-400 -top-4"></div>

                <TreeNode
                  node={child}
                  onNodeClick={onNodeClick}
                  isExpanded={expandedNodes.has(child.user.id)}
                  onToggleExpand={onToggleExpand}
                  parentId={node.user.id}
                  expandedNodes={expandedNodes}
                  selectedNodeId={selectedNodeId}
                  searchTerm={searchTerm}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default function EnhancedReferralTree({
  rootNode,
  loading = false,
  onNodeClick,
  className = ''
}: EnhancedReferralTreeProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [transform, setTransform] = useState({ x: 0, y: 0, scale: 1 })
  const [isExporting, setIsExporting] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const treeRef = useRef<HTMLDivElement>(null)

  // Enhanced hooks
  const navigation = useTreeNavigation(rootNode)
  const search = useTreeSearch(rootNode)
  const { exportTree } = useTreeExport()

  // Use filtered tree if search is active, otherwise use original
  const displayTree = search.isSearchActive ? search.filteredTree : rootNode

  // Combine expanded nodes from navigation and search
  const expandedNodes = useMemo(() => {
    const navExpanded = navigation.expandedNodes
    const searchExpanded = search.getExpandedNodesForSearch()
    return new Set([...navExpanded, ...searchExpanded])
  }, [navigation.expandedNodes, search.getExpandedNodesForSearch])

  // Get node statistics
  const { totalNodes, visibleNodes } = navigation.getNodeStats()

  // Export functions
  const handleExportPNG = useCallback(async () => {
    if (!treeRef.current) return
    setIsExporting(true)
    try {
      await exportTree('referral-tree-container', 'png', {
        filename: `referral-tree-${new Date().toISOString().split('T')[0]}`,
        quality: 1,
        scale: 2
      })
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setIsExporting(false)
    }
  }, [exportTree])

  const handleExportPDF = useCallback(async () => {
    if (!treeRef.current) return
    setIsExporting(true)
    try {
      await exportTree('referral-tree-container', 'pdf', {
        filename: `referral-tree-${new Date().toISOString().split('T')[0]}`,
        quality: 1,
        scale: 2
      })
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setIsExporting(false)
    }
  }, [exportTree])

  // Search result navigation
  const handleNextResult = useCallback(() => {
    const result = search.goToNextResult()
    if (result) {
      navigation.selectNode(result.nodeId)
    }
  }, [search.goToNextResult, navigation.selectNode])

  const handlePreviousResult = useCallback(() => {
    const result = search.goToPreviousResult()
    if (result) {
      navigation.selectNode(result.nodeId)
    }
  }, [search.goToPreviousResult, navigation.selectNode])

  // Zoom functionality with smooth scaling
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault()
    const delta = e.deltaY > 0 ? 0.95 : 1.05
    const newScale = Math.max(0.3, Math.min(2, transform.scale * delta))
    setTransform(prev => ({ ...prev, scale: newScale }))
  }

  const handleZoomIn = useCallback(() => {
    setTransform(prev => ({ ...prev, scale: Math.min(2, prev.scale * 1.1) }))
  }, [])

  const handleZoomOut = useCallback(() => {
    setTransform(prev => ({ ...prev, scale: Math.max(0.3, prev.scale * 0.9) }))
  }, [])

  const handleResetView = useCallback(() => {
    setTransform({ x: 0, y: 0, scale: 1 })
  }, [])

  // Drag functionality with improved responsiveness
  const handleMouseDown = (e: React.MouseEvent) => {
    // Only start drag if clicking on the container background, not on nodes
    const target = e.target as HTMLElement
    const isNodeClick = target.closest('[data-node-card]') || target.closest('button')

    if (isNodeClick) return

    // Prevent text selection during drag
    e.preventDefault()
    e.stopPropagation()

    const startX = e.clientX - transform.x
    const startY = e.clientY - transform.y

    setIsDragging(true)
    setDragStart({ x: startX, y: startY })

    // Add global event listeners for better drag handling
    const handleGlobalMouseMove = (e: MouseEvent) => {
      e.preventDefault()
      const newX = e.clientX - startX
      const newY = e.clientY - startY

      setTransform(prev => ({
        ...prev,
        x: newX,
        y: newY
      }))
    }

    const handleGlobalMouseUp = (e: MouseEvent) => {
      e.preventDefault()
      setIsDragging(false)
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
      document.body.style.cursor = ''
    }

    // Set cursor for entire document
    document.body.style.cursor = 'grabbing'
    document.addEventListener('mousemove', handleGlobalMouseMove, { passive: false })
    document.addEventListener('mouseup', handleGlobalMouseUp, { passive: false })
  }



  if (loading) {
    return (
      <div className={`flex items-center justify-center min-h-[400px] ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading referral tree...</p>
          <p className="text-sm text-gray-500 mt-2">This may take a few moments for large networks</p>
        </div>
      </div>
    )
  }

  if (!rootNode) {
    return (
      <div className={`flex items-center justify-center min-h-[400px] ${className}`}>
        <div className="text-center text-gray-500">
          <Network className="h-16 w-16 mx-auto mb-4 opacity-50" />
          <p>No referral tree data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Enhanced Toolbar */}
      <ReferralTreeToolbar
        searchTerm={search.searchTerm}
        onSearchChange={search.startSearch}
        onClearSearch={search.clearSearch}
        searchResults={search.searchResults.length}
        currentResultIndex={search.currentResultIndex}
        onNextResult={handleNextResult}
        onPreviousResult={handlePreviousResult}
        hasResults={search.hasResults}
        onExpandAll={navigation.expandAll}
        onCollapseAll={navigation.collapseAll}
        onExportPNG={handleExportPNG}
        onExportPDF={handleExportPDF}
        isExporting={isExporting}
        totalNodes={totalNodes}
        visibleNodes={visibleNodes}
        scale={transform.scale}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetView={handleResetView}
      />

      {/* Tree Container */}
      <div className="flex-1 relative bg-gradient-to-br from-blue-50 to-indigo-50 overflow-hidden">
        <div
          ref={containerRef}
          className="w-full h-full overflow-hidden select-none"
          onMouseDown={handleMouseDown}
          onWheel={handleWheel}
          style={{
            cursor: isDragging ? 'grabbing' : 'grab',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none'
          }}
        >
          <div
            id="referral-tree-container"
            ref={treeRef}
            className="flex justify-center items-start min-h-screen p-8 pt-16 transition-transform duration-100"
            style={{
              transform: `translate(${transform.x}px, ${transform.y}px) scale(${transform.scale})`,
              transformOrigin: 'center top'
            }}
          >
            {displayTree && (
              <TreeNode
                node={displayTree}
                onNodeClick={onNodeClick}
                isExpanded={expandedNodes.has(displayTree.user.id)}
                onToggleExpand={navigation.toggleExpand}
                expandedNodes={expandedNodes}
                selectedNodeId={navigation.selectedNodeId}
                searchTerm={search.searchTerm}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
