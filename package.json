{"name": "okdoi-marketplace", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:referral": "jest src/tests/referral-system.test.ts", "test:performance": "jest src/tests/referral-performance.test.ts", "test:referral-all": "ts-node src/scripts/run-referral-tests.ts", "validate:referral": "ts-node src/scripts/validate-referral-integrity.ts"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-slot": "^1.2.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.38.0", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "@types/react-window": "^1.8.8", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^4.1.0", "dotenv": "^17.2.2", "framer-motion": "^12.23.12", "html2canvas": "^1.4.1", "jspdf": "^3.0.2", "lucide-react": "^0.292.0", "next": "^14.2.32", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "react-intersection-observer": "^9.16.0", "react-window": "^2.1.0", "react-window-infinite-loader": "^1.0.10", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "zod": "^4.1.5"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.11", "eslint": "^8.51.0", "eslint-config-next": "14.0.0", "typescript": "^5.2.2"}}