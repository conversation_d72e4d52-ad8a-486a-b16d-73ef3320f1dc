"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/iobuffer";
exports.ids = ["vendor-chunks/iobuffer"];
exports.modules = {

/***/ "(ssr)/./node_modules/iobuffer/lib-esm/IOBuffer.js":
/*!***************************************************!*\
  !*** ./node_modules/iobuffer/lib-esm/IOBuffer.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IOBuffer: () => (/* binding */ IOBuffer)\n/* harmony export */ });\n/* harmony import */ var _text__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text */ \"(ssr)/./node_modules/iobuffer/lib-esm/text.js\");\n\nconst defaultByteLength = 1024 * 8;\nconst hostBigEndian = (() => {\n    const array = new Uint8Array(4);\n    const view = new Uint32Array(array.buffer);\n    return !((view[0] = 1) & array[0]);\n})();\nconst typedArrays = {\n    int8: globalThis.Int8Array,\n    uint8: globalThis.Uint8Array,\n    int16: globalThis.Int16Array,\n    uint16: globalThis.Uint16Array,\n    int32: globalThis.Int32Array,\n    uint32: globalThis.Uint32Array,\n    uint64: globalThis.BigUint64Array,\n    int64: globalThis.BigInt64Array,\n    float32: globalThis.Float32Array,\n    float64: globalThis.Float64Array,\n};\nclass IOBuffer {\n    /**\n     * Reference to the internal ArrayBuffer object.\n     */\n    buffer;\n    /**\n     * Byte length of the internal ArrayBuffer.\n     */\n    byteLength;\n    /**\n     * Byte offset of the internal ArrayBuffer.\n     */\n    byteOffset;\n    /**\n     * Byte length of the internal ArrayBuffer.\n     */\n    length;\n    /**\n     * The current offset of the buffer's pointer.\n     */\n    offset;\n    lastWrittenByte;\n    littleEndian;\n    _data;\n    _mark;\n    _marks;\n    /**\n     * Create a new IOBuffer.\n     * @param data - The data to construct the IOBuffer with.\n     * If data is a number, it will be the new buffer's length<br>\n     * If data is `undefined`, the buffer will be initialized with a default length of 8Kb<br>\n     * If data is an ArrayBuffer, SharedArrayBuffer, an ArrayBufferView (Typed Array), an IOBuffer instance,\n     * or a Node.js Buffer, a view will be created over the underlying ArrayBuffer.\n     * @param options - An object for the options.\n     * @returns A new IOBuffer instance.\n     */\n    constructor(data = defaultByteLength, options = {}) {\n        let dataIsGiven = false;\n        if (typeof data === 'number') {\n            data = new ArrayBuffer(data);\n        }\n        else {\n            dataIsGiven = true;\n            this.lastWrittenByte = data.byteLength;\n        }\n        const offset = options.offset ? options.offset >>> 0 : 0;\n        const byteLength = data.byteLength - offset;\n        let dvOffset = offset;\n        if (ArrayBuffer.isView(data) || data instanceof IOBuffer) {\n            if (data.byteLength !== data.buffer.byteLength) {\n                dvOffset = data.byteOffset + offset;\n            }\n            data = data.buffer;\n        }\n        if (dataIsGiven) {\n            this.lastWrittenByte = byteLength;\n        }\n        else {\n            this.lastWrittenByte = 0;\n        }\n        this.buffer = data;\n        this.length = byteLength;\n        this.byteLength = byteLength;\n        this.byteOffset = dvOffset;\n        this.offset = 0;\n        this.littleEndian = true;\n        this._data = new DataView(this.buffer, dvOffset, byteLength);\n        this._mark = 0;\n        this._marks = [];\n    }\n    /**\n     * Checks if the memory allocated to the buffer is sufficient to store more\n     * bytes after the offset.\n     * @param byteLength - The needed memory in bytes.\n     * @returns `true` if there is sufficient space and `false` otherwise.\n     */\n    available(byteLength = 1) {\n        return this.offset + byteLength <= this.length;\n    }\n    /**\n     * Check if little-endian mode is used for reading and writing multi-byte\n     * values.\n     * @returns `true` if little-endian mode is used, `false` otherwise.\n     */\n    isLittleEndian() {\n        return this.littleEndian;\n    }\n    /**\n     * Set little-endian mode for reading and writing multi-byte values.\n     * @returns This.\n     */\n    setLittleEndian() {\n        this.littleEndian = true;\n        return this;\n    }\n    /**\n     * Check if big-endian mode is used for reading and writing multi-byte values.\n     * @returns `true` if big-endian mode is used, `false` otherwise.\n     */\n    isBigEndian() {\n        return !this.littleEndian;\n    }\n    /**\n     * Switches to big-endian mode for reading and writing multi-byte values.\n     * @returns This.\n     */\n    setBigEndian() {\n        this.littleEndian = false;\n        return this;\n    }\n    /**\n     * Move the pointer n bytes forward.\n     * @param n - Number of bytes to skip.\n     * @returns This.\n     */\n    skip(n = 1) {\n        this.offset += n;\n        return this;\n    }\n    /**\n     * Move the pointer n bytes backward.\n     * @param n - Number of bytes to move back.\n     * @returns This.\n     */\n    back(n = 1) {\n        this.offset -= n;\n        return this;\n    }\n    /**\n     * Move the pointer to the given offset.\n     * @param offset - The offset to move to.\n     * @returns This.\n     */\n    seek(offset) {\n        this.offset = offset;\n        return this;\n    }\n    /**\n     * Store the current pointer offset.\n     * @see {@link IOBuffer#reset}\n     * @returns This.\n     */\n    mark() {\n        this._mark = this.offset;\n        return this;\n    }\n    /**\n     * Move the pointer back to the last pointer offset set by mark.\n     * @see {@link IOBuffer#mark}\n     * @returns This.\n     */\n    reset() {\n        this.offset = this._mark;\n        return this;\n    }\n    /**\n     * Push the current pointer offset to the mark stack.\n     * @see {@link IOBuffer#popMark}\n     * @returns This.\n     */\n    pushMark() {\n        this._marks.push(this.offset);\n        return this;\n    }\n    /**\n     * Pop the last pointer offset from the mark stack, and set the current\n     * pointer offset to the popped value.\n     * @see {@link IOBuffer#pushMark}\n     * @returns This.\n     */\n    popMark() {\n        const offset = this._marks.pop();\n        if (offset === undefined) {\n            throw new Error('Mark stack empty');\n        }\n        this.seek(offset);\n        return this;\n    }\n    /**\n     * Move the pointer offset back to 0.\n     * @returns This.\n     */\n    rewind() {\n        this.offset = 0;\n        return this;\n    }\n    /**\n     * Make sure the buffer has sufficient memory to write a given byteLength at\n     * the current pointer offset.\n     * If the buffer's memory is insufficient, this method will create a new\n     * buffer (a copy) with a length that is twice (byteLength + current offset).\n     * @param byteLength - The needed memory in bytes.\n     * @returns This.\n     */\n    ensureAvailable(byteLength = 1) {\n        if (!this.available(byteLength)) {\n            const lengthNeeded = this.offset + byteLength;\n            const newLength = lengthNeeded * 2;\n            const newArray = new Uint8Array(newLength);\n            newArray.set(new Uint8Array(this.buffer));\n            this.buffer = newArray.buffer;\n            this.length = newLength;\n            this.byteLength = newLength;\n            this._data = new DataView(this.buffer);\n        }\n        return this;\n    }\n    /**\n     * Read a byte and return false if the byte's value is 0, or true otherwise.\n     * Moves pointer forward by one byte.\n     * @returns The read boolean.\n     */\n    readBoolean() {\n        return this.readUint8() !== 0;\n    }\n    /**\n     * Read a signed 8-bit integer and move pointer forward by 1 byte.\n     * @returns The read byte.\n     */\n    readInt8() {\n        return this._data.getInt8(this.offset++);\n    }\n    /**\n     * Read an unsigned 8-bit integer and move pointer forward by 1 byte.\n     * @returns The read byte.\n     */\n    readUint8() {\n        return this._data.getUint8(this.offset++);\n    }\n    /**\n     * Alias for {@link IOBuffer#readUint8}.\n     * @returns The read byte.\n     */\n    readByte() {\n        return this.readUint8();\n    }\n    /**\n     * Read `n` bytes and move pointer forward by `n` bytes.\n     * @param n - Number of bytes to read.\n     * @returns The read bytes.\n     */\n    readBytes(n = 1) {\n        return this.readArray(n, 'uint8');\n    }\n    /**\n     * Creates an array of corresponding to the type `type` and size `size`.\n     * For example type `uint8` will create a `Uint8Array`.\n     * @param size - size of the resulting array\n     * @param type - number type of elements to read\n     * @returns The read array.\n     */\n    readArray(size, type) {\n        const bytes = typedArrays[type].BYTES_PER_ELEMENT * size;\n        const offset = this.byteOffset + this.offset;\n        const slice = this.buffer.slice(offset, offset + bytes);\n        if (this.littleEndian === hostBigEndian &&\n            type !== 'uint8' &&\n            type !== 'int8') {\n            const slice = new Uint8Array(this.buffer.slice(offset, offset + bytes));\n            slice.reverse();\n            const returnArray = new typedArrays[type](slice.buffer);\n            this.offset += bytes;\n            returnArray.reverse();\n            return returnArray;\n        }\n        const returnArray = new typedArrays[type](slice);\n        this.offset += bytes;\n        return returnArray;\n    }\n    /**\n     * Read a 16-bit signed integer and move pointer forward by 2 bytes.\n     * @returns The read value.\n     */\n    readInt16() {\n        const value = this._data.getInt16(this.offset, this.littleEndian);\n        this.offset += 2;\n        return value;\n    }\n    /**\n     * Read a 16-bit unsigned integer and move pointer forward by 2 bytes.\n     * @returns The read value.\n     */\n    readUint16() {\n        const value = this._data.getUint16(this.offset, this.littleEndian);\n        this.offset += 2;\n        return value;\n    }\n    /**\n     * Read a 32-bit signed integer and move pointer forward by 4 bytes.\n     * @returns The read value.\n     */\n    readInt32() {\n        const value = this._data.getInt32(this.offset, this.littleEndian);\n        this.offset += 4;\n        return value;\n    }\n    /**\n     * Read a 32-bit unsigned integer and move pointer forward by 4 bytes.\n     * @returns The read value.\n     */\n    readUint32() {\n        const value = this._data.getUint32(this.offset, this.littleEndian);\n        this.offset += 4;\n        return value;\n    }\n    /**\n     * Read a 32-bit floating number and move pointer forward by 4 bytes.\n     * @returns The read value.\n     */\n    readFloat32() {\n        const value = this._data.getFloat32(this.offset, this.littleEndian);\n        this.offset += 4;\n        return value;\n    }\n    /**\n     * Read a 64-bit floating number and move pointer forward by 8 bytes.\n     * @returns The read value.\n     */\n    readFloat64() {\n        const value = this._data.getFloat64(this.offset, this.littleEndian);\n        this.offset += 8;\n        return value;\n    }\n    /**\n     * Read a 64-bit signed integer number and move pointer forward by 8 bytes.\n     * @returns The read value.\n     */\n    readBigInt64() {\n        const value = this._data.getBigInt64(this.offset, this.littleEndian);\n        this.offset += 8;\n        return value;\n    }\n    /**\n     * Read a 64-bit unsigned integer number and move pointer forward by 8 bytes.\n     * @returns The read value.\n     */\n    readBigUint64() {\n        const value = this._data.getBigUint64(this.offset, this.littleEndian);\n        this.offset += 8;\n        return value;\n    }\n    /**\n     * Read a 1-byte ASCII character and move pointer forward by 1 byte.\n     * @returns The read character.\n     */\n    readChar() {\n        // eslint-disable-next-line unicorn/prefer-code-point\n        return String.fromCharCode(this.readInt8());\n    }\n    /**\n     * Read `n` 1-byte ASCII characters and move pointer forward by `n` bytes.\n     * @param n - Number of characters to read.\n     * @returns The read characters.\n     */\n    readChars(n = 1) {\n        let result = '';\n        for (let i = 0; i < n; i++) {\n            result += this.readChar();\n        }\n        return result;\n    }\n    /**\n     * Read the next `n` bytes, return a UTF-8 decoded string and move pointer\n     * forward by `n` bytes.\n     * @param n - Number of bytes to read.\n     * @returns The decoded string.\n     */\n    readUtf8(n = 1) {\n        return (0,_text__WEBPACK_IMPORTED_MODULE_0__.decode)(this.readBytes(n));\n    }\n    /**\n     * Read the next `n` bytes, return a string decoded with `encoding` and move pointer\n     * forward by `n` bytes.\n     * If no encoding is passed, the function is equivalent to @see {@link IOBuffer#readUtf8}\n     * @param n - Number of bytes to read.\n     * @param encoding - The encoding to use. Default is 'utf8'.\n     * @returns The decoded string.\n     */\n    decodeText(n = 1, encoding = 'utf8') {\n        return (0,_text__WEBPACK_IMPORTED_MODULE_0__.decode)(this.readBytes(n), encoding);\n    }\n    /**\n     * Write 0xff if the passed value is truthy, 0x00 otherwise and move pointer\n     * forward by 1 byte.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeBoolean(value) {\n        this.writeUint8(value ? 0xff : 0x00);\n        return this;\n    }\n    /**\n     * Write `value` as an 8-bit signed integer and move pointer forward by 1 byte.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeInt8(value) {\n        this.ensureAvailable(1);\n        this._data.setInt8(this.offset++, value);\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write `value` as an 8-bit unsigned integer and move pointer forward by 1\n     * byte.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeUint8(value) {\n        this.ensureAvailable(1);\n        this._data.setUint8(this.offset++, value);\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * An alias for {@link IOBuffer#writeUint8}.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeByte(value) {\n        return this.writeUint8(value);\n    }\n    /**\n     * Write all elements of `bytes` as uint8 values and move pointer forward by\n     * `bytes.length` bytes.\n     * @param bytes - The array of bytes to write.\n     * @returns This.\n     */\n    writeBytes(bytes) {\n        this.ensureAvailable(bytes.length);\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < bytes.length; i++) {\n            this._data.setUint8(this.offset++, bytes[i]);\n        }\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write `value` as a 16-bit signed integer and move pointer forward by 2\n     * bytes.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeInt16(value) {\n        this.ensureAvailable(2);\n        this._data.setInt16(this.offset, value, this.littleEndian);\n        this.offset += 2;\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write `value` as a 16-bit unsigned integer and move pointer forward by 2\n     * bytes.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeUint16(value) {\n        this.ensureAvailable(2);\n        this._data.setUint16(this.offset, value, this.littleEndian);\n        this.offset += 2;\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write `value` as a 32-bit signed integer and move pointer forward by 4\n     * bytes.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeInt32(value) {\n        this.ensureAvailable(4);\n        this._data.setInt32(this.offset, value, this.littleEndian);\n        this.offset += 4;\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write `value` as a 32-bit unsigned integer and move pointer forward by 4\n     * bytes.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeUint32(value) {\n        this.ensureAvailable(4);\n        this._data.setUint32(this.offset, value, this.littleEndian);\n        this.offset += 4;\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write `value` as a 32-bit floating number and move pointer forward by 4\n     * bytes.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeFloat32(value) {\n        this.ensureAvailable(4);\n        this._data.setFloat32(this.offset, value, this.littleEndian);\n        this.offset += 4;\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write `value` as a 64-bit floating number and move pointer forward by 8\n     * bytes.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeFloat64(value) {\n        this.ensureAvailable(8);\n        this._data.setFloat64(this.offset, value, this.littleEndian);\n        this.offset += 8;\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write `value` as a 64-bit signed bigint and move pointer forward by 8\n     * bytes.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeBigInt64(value) {\n        this.ensureAvailable(8);\n        this._data.setBigInt64(this.offset, value, this.littleEndian);\n        this.offset += 8;\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write `value` as a 64-bit unsigned bigint and move pointer forward by 8\n     * bytes.\n     * @param value - The value to write.\n     * @returns This.\n     */\n    writeBigUint64(value) {\n        this.ensureAvailable(8);\n        this._data.setBigUint64(this.offset, value, this.littleEndian);\n        this.offset += 8;\n        this._updateLastWrittenByte();\n        return this;\n    }\n    /**\n     * Write the charCode of `str`'s first character as an 8-bit unsigned integer\n     * and move pointer forward by 1 byte.\n     * @param str - The character to write.\n     * @returns This.\n     */\n    writeChar(str) {\n        // eslint-disable-next-line unicorn/prefer-code-point\n        return this.writeUint8(str.charCodeAt(0));\n    }\n    /**\n     * Write the charCodes of all `str`'s characters as 8-bit unsigned integers\n     * and move pointer forward by `str.length` bytes.\n     * @param str - The characters to write.\n     * @returns This.\n     */\n    writeChars(str) {\n        for (let i = 0; i < str.length; i++) {\n            // eslint-disable-next-line unicorn/prefer-code-point\n            this.writeUint8(str.charCodeAt(i));\n        }\n        return this;\n    }\n    /**\n     * UTF-8 encode and write `str` to the current pointer offset and move pointer\n     * forward according to the encoded length.\n     * @param str - The string to write.\n     * @returns This.\n     */\n    writeUtf8(str) {\n        return this.writeBytes((0,_text__WEBPACK_IMPORTED_MODULE_0__.encode)(str));\n    }\n    /**\n     * Export a Uint8Array view of the internal buffer.\n     * The view starts at the byte offset and its length\n     * is calculated to stop at the last written byte or the original length.\n     * @returns A new Uint8Array view.\n     */\n    toArray() {\n        return new Uint8Array(this.buffer, this.byteOffset, this.lastWrittenByte);\n    }\n    /**\n     *  Get the total number of bytes written so far, regardless of the current offset.\n     * @returns - Total number of bytes.\n     */\n    getWrittenByteLength() {\n        return this.lastWrittenByte - this.byteOffset;\n    }\n    /**\n     * Update the last written byte offset\n     * @private\n     */\n    _updateLastWrittenByte() {\n        if (this.offset > this.lastWrittenByte) {\n            this.lastWrittenByte = this.offset;\n        }\n    }\n}\n//# sourceMappingURL=IOBuffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaW9idWZmZXIvbGliLWVzbS9JT0J1ZmZlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRDtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHlCQUF5QjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU87QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsNkNBQU07QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxRUFBcUU7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsNkNBQU07QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiwwQkFBMEI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGtCQUFrQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0JBQWdCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQiw2Q0FBTTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29rZG9pLW1hcmtldHBsYWNlLy4vbm9kZV9tb2R1bGVzL2lvYnVmZmVyL2xpYi1lc20vSU9CdWZmZXIuanM/N2I4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWNvZGUsIGVuY29kZSB9IGZyb20gJy4vdGV4dCc7XG5jb25zdCBkZWZhdWx0Qnl0ZUxlbmd0aCA9IDEwMjQgKiA4O1xuY29uc3QgaG9zdEJpZ0VuZGlhbiA9ICgoKSA9PiB7XG4gICAgY29uc3QgYXJyYXkgPSBuZXcgVWludDhBcnJheSg0KTtcbiAgICBjb25zdCB2aWV3ID0gbmV3IFVpbnQzMkFycmF5KGFycmF5LmJ1ZmZlcik7XG4gICAgcmV0dXJuICEoKHZpZXdbMF0gPSAxKSAmIGFycmF5WzBdKTtcbn0pKCk7XG5jb25zdCB0eXBlZEFycmF5cyA9IHtcbiAgICBpbnQ4OiBnbG9iYWxUaGlzLkludDhBcnJheSxcbiAgICB1aW50ODogZ2xvYmFsVGhpcy5VaW50OEFycmF5LFxuICAgIGludDE2OiBnbG9iYWxUaGlzLkludDE2QXJyYXksXG4gICAgdWludDE2OiBnbG9iYWxUaGlzLlVpbnQxNkFycmF5LFxuICAgIGludDMyOiBnbG9iYWxUaGlzLkludDMyQXJyYXksXG4gICAgdWludDMyOiBnbG9iYWxUaGlzLlVpbnQzMkFycmF5LFxuICAgIHVpbnQ2NDogZ2xvYmFsVGhpcy5CaWdVaW50NjRBcnJheSxcbiAgICBpbnQ2NDogZ2xvYmFsVGhpcy5CaWdJbnQ2NEFycmF5LFxuICAgIGZsb2F0MzI6IGdsb2JhbFRoaXMuRmxvYXQzMkFycmF5LFxuICAgIGZsb2F0NjQ6IGdsb2JhbFRoaXMuRmxvYXQ2NEFycmF5LFxufTtcbmV4cG9ydCBjbGFzcyBJT0J1ZmZlciB7XG4gICAgLyoqXG4gICAgICogUmVmZXJlbmNlIHRvIHRoZSBpbnRlcm5hbCBBcnJheUJ1ZmZlciBvYmplY3QuXG4gICAgICovXG4gICAgYnVmZmVyO1xuICAgIC8qKlxuICAgICAqIEJ5dGUgbGVuZ3RoIG9mIHRoZSBpbnRlcm5hbCBBcnJheUJ1ZmZlci5cbiAgICAgKi9cbiAgICBieXRlTGVuZ3RoO1xuICAgIC8qKlxuICAgICAqIEJ5dGUgb2Zmc2V0IG9mIHRoZSBpbnRlcm5hbCBBcnJheUJ1ZmZlci5cbiAgICAgKi9cbiAgICBieXRlT2Zmc2V0O1xuICAgIC8qKlxuICAgICAqIEJ5dGUgbGVuZ3RoIG9mIHRoZSBpbnRlcm5hbCBBcnJheUJ1ZmZlci5cbiAgICAgKi9cbiAgICBsZW5ndGg7XG4gICAgLyoqXG4gICAgICogVGhlIGN1cnJlbnQgb2Zmc2V0IG9mIHRoZSBidWZmZXIncyBwb2ludGVyLlxuICAgICAqL1xuICAgIG9mZnNldDtcbiAgICBsYXN0V3JpdHRlbkJ5dGU7XG4gICAgbGl0dGxlRW5kaWFuO1xuICAgIF9kYXRhO1xuICAgIF9tYXJrO1xuICAgIF9tYXJrcztcbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSBuZXcgSU9CdWZmZXIuXG4gICAgICogQHBhcmFtIGRhdGEgLSBUaGUgZGF0YSB0byBjb25zdHJ1Y3QgdGhlIElPQnVmZmVyIHdpdGguXG4gICAgICogSWYgZGF0YSBpcyBhIG51bWJlciwgaXQgd2lsbCBiZSB0aGUgbmV3IGJ1ZmZlcidzIGxlbmd0aDxicj5cbiAgICAgKiBJZiBkYXRhIGlzIGB1bmRlZmluZWRgLCB0aGUgYnVmZmVyIHdpbGwgYmUgaW5pdGlhbGl6ZWQgd2l0aCBhIGRlZmF1bHQgbGVuZ3RoIG9mIDhLYjxicj5cbiAgICAgKiBJZiBkYXRhIGlzIGFuIEFycmF5QnVmZmVyLCBTaGFyZWRBcnJheUJ1ZmZlciwgYW4gQXJyYXlCdWZmZXJWaWV3IChUeXBlZCBBcnJheSksIGFuIElPQnVmZmVyIGluc3RhbmNlLFxuICAgICAqIG9yIGEgTm9kZS5qcyBCdWZmZXIsIGEgdmlldyB3aWxsIGJlIGNyZWF0ZWQgb3ZlciB0aGUgdW5kZXJseWluZyBBcnJheUJ1ZmZlci5cbiAgICAgKiBAcGFyYW0gb3B0aW9ucyAtIEFuIG9iamVjdCBmb3IgdGhlIG9wdGlvbnMuXG4gICAgICogQHJldHVybnMgQSBuZXcgSU9CdWZmZXIgaW5zdGFuY2UuXG4gICAgICovXG4gICAgY29uc3RydWN0b3IoZGF0YSA9IGRlZmF1bHRCeXRlTGVuZ3RoLCBvcHRpb25zID0ge30pIHtcbiAgICAgICAgbGV0IGRhdGFJc0dpdmVuID0gZmFsc2U7XG4gICAgICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgIGRhdGEgPSBuZXcgQXJyYXlCdWZmZXIoZGF0YSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBkYXRhSXNHaXZlbiA9IHRydWU7XG4gICAgICAgICAgICB0aGlzLmxhc3RXcml0dGVuQnl0ZSA9IGRhdGEuYnl0ZUxlbmd0aDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBvZmZzZXQgPSBvcHRpb25zLm9mZnNldCA/IG9wdGlvbnMub2Zmc2V0ID4+PiAwIDogMDtcbiAgICAgICAgY29uc3QgYnl0ZUxlbmd0aCA9IGRhdGEuYnl0ZUxlbmd0aCAtIG9mZnNldDtcbiAgICAgICAgbGV0IGR2T2Zmc2V0ID0gb2Zmc2V0O1xuICAgICAgICBpZiAoQXJyYXlCdWZmZXIuaXNWaWV3KGRhdGEpIHx8IGRhdGEgaW5zdGFuY2VvZiBJT0J1ZmZlcikge1xuICAgICAgICAgICAgaWYgKGRhdGEuYnl0ZUxlbmd0aCAhPT0gZGF0YS5idWZmZXIuYnl0ZUxlbmd0aCkge1xuICAgICAgICAgICAgICAgIGR2T2Zmc2V0ID0gZGF0YS5ieXRlT2Zmc2V0ICsgb2Zmc2V0O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZGF0YSA9IGRhdGEuYnVmZmVyO1xuICAgICAgICB9XG4gICAgICAgIGlmIChkYXRhSXNHaXZlbikge1xuICAgICAgICAgICAgdGhpcy5sYXN0V3JpdHRlbkJ5dGUgPSBieXRlTGVuZ3RoO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5sYXN0V3JpdHRlbkJ5dGUgPSAwO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuYnVmZmVyID0gZGF0YTtcbiAgICAgICAgdGhpcy5sZW5ndGggPSBieXRlTGVuZ3RoO1xuICAgICAgICB0aGlzLmJ5dGVMZW5ndGggPSBieXRlTGVuZ3RoO1xuICAgICAgICB0aGlzLmJ5dGVPZmZzZXQgPSBkdk9mZnNldDtcbiAgICAgICAgdGhpcy5vZmZzZXQgPSAwO1xuICAgICAgICB0aGlzLmxpdHRsZUVuZGlhbiA9IHRydWU7XG4gICAgICAgIHRoaXMuX2RhdGEgPSBuZXcgRGF0YVZpZXcodGhpcy5idWZmZXIsIGR2T2Zmc2V0LCBieXRlTGVuZ3RoKTtcbiAgICAgICAgdGhpcy5fbWFyayA9IDA7XG4gICAgICAgIHRoaXMuX21hcmtzID0gW107XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENoZWNrcyBpZiB0aGUgbWVtb3J5IGFsbG9jYXRlZCB0byB0aGUgYnVmZmVyIGlzIHN1ZmZpY2llbnQgdG8gc3RvcmUgbW9yZVxuICAgICAqIGJ5dGVzIGFmdGVyIHRoZSBvZmZzZXQuXG4gICAgICogQHBhcmFtIGJ5dGVMZW5ndGggLSBUaGUgbmVlZGVkIG1lbW9yeSBpbiBieXRlcy5cbiAgICAgKiBAcmV0dXJucyBgdHJ1ZWAgaWYgdGhlcmUgaXMgc3VmZmljaWVudCBzcGFjZSBhbmQgYGZhbHNlYCBvdGhlcndpc2UuXG4gICAgICovXG4gICAgYXZhaWxhYmxlKGJ5dGVMZW5ndGggPSAxKSB7XG4gICAgICAgIHJldHVybiB0aGlzLm9mZnNldCArIGJ5dGVMZW5ndGggPD0gdGhpcy5sZW5ndGg7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENoZWNrIGlmIGxpdHRsZS1lbmRpYW4gbW9kZSBpcyB1c2VkIGZvciByZWFkaW5nIGFuZCB3cml0aW5nIG11bHRpLWJ5dGVcbiAgICAgKiB2YWx1ZXMuXG4gICAgICogQHJldHVybnMgYHRydWVgIGlmIGxpdHRsZS1lbmRpYW4gbW9kZSBpcyB1c2VkLCBgZmFsc2VgIG90aGVyd2lzZS5cbiAgICAgKi9cbiAgICBpc0xpdHRsZUVuZGlhbigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubGl0dGxlRW5kaWFuO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZXQgbGl0dGxlLWVuZGlhbiBtb2RlIGZvciByZWFkaW5nIGFuZCB3cml0aW5nIG11bHRpLWJ5dGUgdmFsdWVzLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgc2V0TGl0dGxlRW5kaWFuKCkge1xuICAgICAgICB0aGlzLmxpdHRsZUVuZGlhbiA9IHRydWU7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDaGVjayBpZiBiaWctZW5kaWFuIG1vZGUgaXMgdXNlZCBmb3IgcmVhZGluZyBhbmQgd3JpdGluZyBtdWx0aS1ieXRlIHZhbHVlcy5cbiAgICAgKiBAcmV0dXJucyBgdHJ1ZWAgaWYgYmlnLWVuZGlhbiBtb2RlIGlzIHVzZWQsIGBmYWxzZWAgb3RoZXJ3aXNlLlxuICAgICAqL1xuICAgIGlzQmlnRW5kaWFuKCkge1xuICAgICAgICByZXR1cm4gIXRoaXMubGl0dGxlRW5kaWFuO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTd2l0Y2hlcyB0byBiaWctZW5kaWFuIG1vZGUgZm9yIHJlYWRpbmcgYW5kIHdyaXRpbmcgbXVsdGktYnl0ZSB2YWx1ZXMuXG4gICAgICogQHJldHVybnMgVGhpcy5cbiAgICAgKi9cbiAgICBzZXRCaWdFbmRpYW4oKSB7XG4gICAgICAgIHRoaXMubGl0dGxlRW5kaWFuID0gZmFsc2U7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBNb3ZlIHRoZSBwb2ludGVyIG4gYnl0ZXMgZm9yd2FyZC5cbiAgICAgKiBAcGFyYW0gbiAtIE51bWJlciBvZiBieXRlcyB0byBza2lwLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgc2tpcChuID0gMSkge1xuICAgICAgICB0aGlzLm9mZnNldCArPSBuO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogTW92ZSB0aGUgcG9pbnRlciBuIGJ5dGVzIGJhY2t3YXJkLlxuICAgICAqIEBwYXJhbSBuIC0gTnVtYmVyIG9mIGJ5dGVzIHRvIG1vdmUgYmFjay5cbiAgICAgKiBAcmV0dXJucyBUaGlzLlxuICAgICAqL1xuICAgIGJhY2sobiA9IDEpIHtcbiAgICAgICAgdGhpcy5vZmZzZXQgLT0gbjtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIE1vdmUgdGhlIHBvaW50ZXIgdG8gdGhlIGdpdmVuIG9mZnNldC5cbiAgICAgKiBAcGFyYW0gb2Zmc2V0IC0gVGhlIG9mZnNldCB0byBtb3ZlIHRvLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgc2VlayhvZmZzZXQpIHtcbiAgICAgICAgdGhpcy5vZmZzZXQgPSBvZmZzZXQ7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTdG9yZSB0aGUgY3VycmVudCBwb2ludGVyIG9mZnNldC5cbiAgICAgKiBAc2VlIHtAbGluayBJT0J1ZmZlciNyZXNldH1cbiAgICAgKiBAcmV0dXJucyBUaGlzLlxuICAgICAqL1xuICAgIG1hcmsoKSB7XG4gICAgICAgIHRoaXMuX21hcmsgPSB0aGlzLm9mZnNldDtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIE1vdmUgdGhlIHBvaW50ZXIgYmFjayB0byB0aGUgbGFzdCBwb2ludGVyIG9mZnNldCBzZXQgYnkgbWFyay5cbiAgICAgKiBAc2VlIHtAbGluayBJT0J1ZmZlciNtYXJrfVxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgcmVzZXQoKSB7XG4gICAgICAgIHRoaXMub2Zmc2V0ID0gdGhpcy5fbWFyaztcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFB1c2ggdGhlIGN1cnJlbnQgcG9pbnRlciBvZmZzZXQgdG8gdGhlIG1hcmsgc3RhY2suXG4gICAgICogQHNlZSB7QGxpbmsgSU9CdWZmZXIjcG9wTWFya31cbiAgICAgKiBAcmV0dXJucyBUaGlzLlxuICAgICAqL1xuICAgIHB1c2hNYXJrKCkge1xuICAgICAgICB0aGlzLl9tYXJrcy5wdXNoKHRoaXMub2Zmc2V0KTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFBvcCB0aGUgbGFzdCBwb2ludGVyIG9mZnNldCBmcm9tIHRoZSBtYXJrIHN0YWNrLCBhbmQgc2V0IHRoZSBjdXJyZW50XG4gICAgICogcG9pbnRlciBvZmZzZXQgdG8gdGhlIHBvcHBlZCB2YWx1ZS5cbiAgICAgKiBAc2VlIHtAbGluayBJT0J1ZmZlciNwdXNoTWFya31cbiAgICAgKiBAcmV0dXJucyBUaGlzLlxuICAgICAqL1xuICAgIHBvcE1hcmsoKSB7XG4gICAgICAgIGNvbnN0IG9mZnNldCA9IHRoaXMuX21hcmtzLnBvcCgpO1xuICAgICAgICBpZiAob2Zmc2V0ID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignTWFyayBzdGFjayBlbXB0eScpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuc2VlayhvZmZzZXQpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogTW92ZSB0aGUgcG9pbnRlciBvZmZzZXQgYmFjayB0byAwLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgcmV3aW5kKCkge1xuICAgICAgICB0aGlzLm9mZnNldCA9IDA7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBNYWtlIHN1cmUgdGhlIGJ1ZmZlciBoYXMgc3VmZmljaWVudCBtZW1vcnkgdG8gd3JpdGUgYSBnaXZlbiBieXRlTGVuZ3RoIGF0XG4gICAgICogdGhlIGN1cnJlbnQgcG9pbnRlciBvZmZzZXQuXG4gICAgICogSWYgdGhlIGJ1ZmZlcidzIG1lbW9yeSBpcyBpbnN1ZmZpY2llbnQsIHRoaXMgbWV0aG9kIHdpbGwgY3JlYXRlIGEgbmV3XG4gICAgICogYnVmZmVyIChhIGNvcHkpIHdpdGggYSBsZW5ndGggdGhhdCBpcyB0d2ljZSAoYnl0ZUxlbmd0aCArIGN1cnJlbnQgb2Zmc2V0KS5cbiAgICAgKiBAcGFyYW0gYnl0ZUxlbmd0aCAtIFRoZSBuZWVkZWQgbWVtb3J5IGluIGJ5dGVzLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgZW5zdXJlQXZhaWxhYmxlKGJ5dGVMZW5ndGggPSAxKSB7XG4gICAgICAgIGlmICghdGhpcy5hdmFpbGFibGUoYnl0ZUxlbmd0aCkpIHtcbiAgICAgICAgICAgIGNvbnN0IGxlbmd0aE5lZWRlZCA9IHRoaXMub2Zmc2V0ICsgYnl0ZUxlbmd0aDtcbiAgICAgICAgICAgIGNvbnN0IG5ld0xlbmd0aCA9IGxlbmd0aE5lZWRlZCAqIDI7XG4gICAgICAgICAgICBjb25zdCBuZXdBcnJheSA9IG5ldyBVaW50OEFycmF5KG5ld0xlbmd0aCk7XG4gICAgICAgICAgICBuZXdBcnJheS5zZXQobmV3IFVpbnQ4QXJyYXkodGhpcy5idWZmZXIpKTtcbiAgICAgICAgICAgIHRoaXMuYnVmZmVyID0gbmV3QXJyYXkuYnVmZmVyO1xuICAgICAgICAgICAgdGhpcy5sZW5ndGggPSBuZXdMZW5ndGg7XG4gICAgICAgICAgICB0aGlzLmJ5dGVMZW5ndGggPSBuZXdMZW5ndGg7XG4gICAgICAgICAgICB0aGlzLl9kYXRhID0gbmV3IERhdGFWaWV3KHRoaXMuYnVmZmVyKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCBhIGJ5dGUgYW5kIHJldHVybiBmYWxzZSBpZiB0aGUgYnl0ZSdzIHZhbHVlIGlzIDAsIG9yIHRydWUgb3RoZXJ3aXNlLlxuICAgICAqIE1vdmVzIHBvaW50ZXIgZm9yd2FyZCBieSBvbmUgYnl0ZS5cbiAgICAgKiBAcmV0dXJucyBUaGUgcmVhZCBib29sZWFuLlxuICAgICAqL1xuICAgIHJlYWRCb29sZWFuKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5yZWFkVWludDgoKSAhPT0gMDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCBhIHNpZ25lZCA4LWJpdCBpbnRlZ2VyIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSAxIGJ5dGUuXG4gICAgICogQHJldHVybnMgVGhlIHJlYWQgYnl0ZS5cbiAgICAgKi9cbiAgICByZWFkSW50OCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2RhdGEuZ2V0SW50OCh0aGlzLm9mZnNldCsrKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCBhbiB1bnNpZ25lZCA4LWJpdCBpbnRlZ2VyIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSAxIGJ5dGUuXG4gICAgICogQHJldHVybnMgVGhlIHJlYWQgYnl0ZS5cbiAgICAgKi9cbiAgICByZWFkVWludDgoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9kYXRhLmdldFVpbnQ4KHRoaXMub2Zmc2V0KyspO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBbGlhcyBmb3Ige0BsaW5rIElPQnVmZmVyI3JlYWRVaW50OH0uXG4gICAgICogQHJldHVybnMgVGhlIHJlYWQgYnl0ZS5cbiAgICAgKi9cbiAgICByZWFkQnl0ZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucmVhZFVpbnQ4KCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYG5gIGJ5dGVzIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSBgbmAgYnl0ZXMuXG4gICAgICogQHBhcmFtIG4gLSBOdW1iZXIgb2YgYnl0ZXMgdG8gcmVhZC5cbiAgICAgKiBAcmV0dXJucyBUaGUgcmVhZCBieXRlcy5cbiAgICAgKi9cbiAgICByZWFkQnl0ZXMobiA9IDEpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucmVhZEFycmF5KG4sICd1aW50OCcpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDcmVhdGVzIGFuIGFycmF5IG9mIGNvcnJlc3BvbmRpbmcgdG8gdGhlIHR5cGUgYHR5cGVgIGFuZCBzaXplIGBzaXplYC5cbiAgICAgKiBGb3IgZXhhbXBsZSB0eXBlIGB1aW50OGAgd2lsbCBjcmVhdGUgYSBgVWludDhBcnJheWAuXG4gICAgICogQHBhcmFtIHNpemUgLSBzaXplIG9mIHRoZSByZXN1bHRpbmcgYXJyYXlcbiAgICAgKiBAcGFyYW0gdHlwZSAtIG51bWJlciB0eXBlIG9mIGVsZW1lbnRzIHRvIHJlYWRcbiAgICAgKiBAcmV0dXJucyBUaGUgcmVhZCBhcnJheS5cbiAgICAgKi9cbiAgICByZWFkQXJyYXkoc2l6ZSwgdHlwZSkge1xuICAgICAgICBjb25zdCBieXRlcyA9IHR5cGVkQXJyYXlzW3R5cGVdLkJZVEVTX1BFUl9FTEVNRU5UICogc2l6ZTtcbiAgICAgICAgY29uc3Qgb2Zmc2V0ID0gdGhpcy5ieXRlT2Zmc2V0ICsgdGhpcy5vZmZzZXQ7XG4gICAgICAgIGNvbnN0IHNsaWNlID0gdGhpcy5idWZmZXIuc2xpY2Uob2Zmc2V0LCBvZmZzZXQgKyBieXRlcyk7XG4gICAgICAgIGlmICh0aGlzLmxpdHRsZUVuZGlhbiA9PT0gaG9zdEJpZ0VuZGlhbiAmJlxuICAgICAgICAgICAgdHlwZSAhPT0gJ3VpbnQ4JyAmJlxuICAgICAgICAgICAgdHlwZSAhPT0gJ2ludDgnKSB7XG4gICAgICAgICAgICBjb25zdCBzbGljZSA9IG5ldyBVaW50OEFycmF5KHRoaXMuYnVmZmVyLnNsaWNlKG9mZnNldCwgb2Zmc2V0ICsgYnl0ZXMpKTtcbiAgICAgICAgICAgIHNsaWNlLnJldmVyc2UoKTtcbiAgICAgICAgICAgIGNvbnN0IHJldHVybkFycmF5ID0gbmV3IHR5cGVkQXJyYXlzW3R5cGVdKHNsaWNlLmJ1ZmZlcik7XG4gICAgICAgICAgICB0aGlzLm9mZnNldCArPSBieXRlcztcbiAgICAgICAgICAgIHJldHVybkFycmF5LnJldmVyc2UoKTtcbiAgICAgICAgICAgIHJldHVybiByZXR1cm5BcnJheTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByZXR1cm5BcnJheSA9IG5ldyB0eXBlZEFycmF5c1t0eXBlXShzbGljZSk7XG4gICAgICAgIHRoaXMub2Zmc2V0ICs9IGJ5dGVzO1xuICAgICAgICByZXR1cm4gcmV0dXJuQXJyYXk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYSAxNi1iaXQgc2lnbmVkIGludGVnZXIgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDIgYnl0ZXMuXG4gICAgICogQHJldHVybnMgVGhlIHJlYWQgdmFsdWUuXG4gICAgICovXG4gICAgcmVhZEludDE2KCkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHRoaXMuX2RhdGEuZ2V0SW50MTYodGhpcy5vZmZzZXQsIHRoaXMubGl0dGxlRW5kaWFuKTtcbiAgICAgICAgdGhpcy5vZmZzZXQgKz0gMjtcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkIGEgMTYtYml0IHVuc2lnbmVkIGludGVnZXIgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDIgYnl0ZXMuXG4gICAgICogQHJldHVybnMgVGhlIHJlYWQgdmFsdWUuXG4gICAgICovXG4gICAgcmVhZFVpbnQxNigpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLl9kYXRhLmdldFVpbnQxNih0aGlzLm9mZnNldCwgdGhpcy5saXR0bGVFbmRpYW4pO1xuICAgICAgICB0aGlzLm9mZnNldCArPSAyO1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYSAzMi1iaXQgc2lnbmVkIGludGVnZXIgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDQgYnl0ZXMuXG4gICAgICogQHJldHVybnMgVGhlIHJlYWQgdmFsdWUuXG4gICAgICovXG4gICAgcmVhZEludDMyKCkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHRoaXMuX2RhdGEuZ2V0SW50MzIodGhpcy5vZmZzZXQsIHRoaXMubGl0dGxlRW5kaWFuKTtcbiAgICAgICAgdGhpcy5vZmZzZXQgKz0gNDtcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkIGEgMzItYml0IHVuc2lnbmVkIGludGVnZXIgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDQgYnl0ZXMuXG4gICAgICogQHJldHVybnMgVGhlIHJlYWQgdmFsdWUuXG4gICAgICovXG4gICAgcmVhZFVpbnQzMigpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLl9kYXRhLmdldFVpbnQzMih0aGlzLm9mZnNldCwgdGhpcy5saXR0bGVFbmRpYW4pO1xuICAgICAgICB0aGlzLm9mZnNldCArPSA0O1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYSAzMi1iaXQgZmxvYXRpbmcgbnVtYmVyIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSA0IGJ5dGVzLlxuICAgICAqIEByZXR1cm5zIFRoZSByZWFkIHZhbHVlLlxuICAgICAqL1xuICAgIHJlYWRGbG9hdDMyKCkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHRoaXMuX2RhdGEuZ2V0RmxvYXQzMih0aGlzLm9mZnNldCwgdGhpcy5saXR0bGVFbmRpYW4pO1xuICAgICAgICB0aGlzLm9mZnNldCArPSA0O1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYSA2NC1iaXQgZmxvYXRpbmcgbnVtYmVyIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSA4IGJ5dGVzLlxuICAgICAqIEByZXR1cm5zIFRoZSByZWFkIHZhbHVlLlxuICAgICAqL1xuICAgIHJlYWRGbG9hdDY0KCkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHRoaXMuX2RhdGEuZ2V0RmxvYXQ2NCh0aGlzLm9mZnNldCwgdGhpcy5saXR0bGVFbmRpYW4pO1xuICAgICAgICB0aGlzLm9mZnNldCArPSA4O1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYSA2NC1iaXQgc2lnbmVkIGludGVnZXIgbnVtYmVyIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSA4IGJ5dGVzLlxuICAgICAqIEByZXR1cm5zIFRoZSByZWFkIHZhbHVlLlxuICAgICAqL1xuICAgIHJlYWRCaWdJbnQ2NCgpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLl9kYXRhLmdldEJpZ0ludDY0KHRoaXMub2Zmc2V0LCB0aGlzLmxpdHRsZUVuZGlhbik7XG4gICAgICAgIHRoaXMub2Zmc2V0ICs9IDg7XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCBhIDY0LWJpdCB1bnNpZ25lZCBpbnRlZ2VyIG51bWJlciBhbmQgbW92ZSBwb2ludGVyIGZvcndhcmQgYnkgOCBieXRlcy5cbiAgICAgKiBAcmV0dXJucyBUaGUgcmVhZCB2YWx1ZS5cbiAgICAgKi9cbiAgICByZWFkQmlnVWludDY0KCkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHRoaXMuX2RhdGEuZ2V0QmlnVWludDY0KHRoaXMub2Zmc2V0LCB0aGlzLmxpdHRsZUVuZGlhbik7XG4gICAgICAgIHRoaXMub2Zmc2V0ICs9IDg7XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCBhIDEtYnl0ZSBBU0NJSSBjaGFyYWN0ZXIgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDEgYnl0ZS5cbiAgICAgKiBAcmV0dXJucyBUaGUgcmVhZCBjaGFyYWN0ZXIuXG4gICAgICovXG4gICAgcmVhZENoYXIoKSB7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSB1bmljb3JuL3ByZWZlci1jb2RlLXBvaW50XG4gICAgICAgIHJldHVybiBTdHJpbmcuZnJvbUNoYXJDb2RlKHRoaXMucmVhZEludDgoKSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYG5gIDEtYnl0ZSBBU0NJSSBjaGFyYWN0ZXJzIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSBgbmAgYnl0ZXMuXG4gICAgICogQHBhcmFtIG4gLSBOdW1iZXIgb2YgY2hhcmFjdGVycyB0byByZWFkLlxuICAgICAqIEByZXR1cm5zIFRoZSByZWFkIGNoYXJhY3RlcnMuXG4gICAgICovXG4gICAgcmVhZENoYXJzKG4gPSAxKSB7XG4gICAgICAgIGxldCByZXN1bHQgPSAnJztcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBuOyBpKyspIHtcbiAgICAgICAgICAgIHJlc3VsdCArPSB0aGlzLnJlYWRDaGFyKCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCB0aGUgbmV4dCBgbmAgYnl0ZXMsIHJldHVybiBhIFVURi04IGRlY29kZWQgc3RyaW5nIGFuZCBtb3ZlIHBvaW50ZXJcbiAgICAgKiBmb3J3YXJkIGJ5IGBuYCBieXRlcy5cbiAgICAgKiBAcGFyYW0gbiAtIE51bWJlciBvZiBieXRlcyB0byByZWFkLlxuICAgICAqIEByZXR1cm5zIFRoZSBkZWNvZGVkIHN0cmluZy5cbiAgICAgKi9cbiAgICByZWFkVXRmOChuID0gMSkge1xuICAgICAgICByZXR1cm4gZGVjb2RlKHRoaXMucmVhZEJ5dGVzKG4pKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCB0aGUgbmV4dCBgbmAgYnl0ZXMsIHJldHVybiBhIHN0cmluZyBkZWNvZGVkIHdpdGggYGVuY29kaW5nYCBhbmQgbW92ZSBwb2ludGVyXG4gICAgICogZm9yd2FyZCBieSBgbmAgYnl0ZXMuXG4gICAgICogSWYgbm8gZW5jb2RpbmcgaXMgcGFzc2VkLCB0aGUgZnVuY3Rpb24gaXMgZXF1aXZhbGVudCB0byBAc2VlIHtAbGluayBJT0J1ZmZlciNyZWFkVXRmOH1cbiAgICAgKiBAcGFyYW0gbiAtIE51bWJlciBvZiBieXRlcyB0byByZWFkLlxuICAgICAqIEBwYXJhbSBlbmNvZGluZyAtIFRoZSBlbmNvZGluZyB0byB1c2UuIERlZmF1bHQgaXMgJ3V0ZjgnLlxuICAgICAqIEByZXR1cm5zIFRoZSBkZWNvZGVkIHN0cmluZy5cbiAgICAgKi9cbiAgICBkZWNvZGVUZXh0KG4gPSAxLCBlbmNvZGluZyA9ICd1dGY4Jykge1xuICAgICAgICByZXR1cm4gZGVjb2RlKHRoaXMucmVhZEJ5dGVzKG4pLCBlbmNvZGluZyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdyaXRlIDB4ZmYgaWYgdGhlIHBhc3NlZCB2YWx1ZSBpcyB0cnV0aHksIDB4MDAgb3RoZXJ3aXNlIGFuZCBtb3ZlIHBvaW50ZXJcbiAgICAgKiBmb3J3YXJkIGJ5IDEgYnl0ZS5cbiAgICAgKiBAcGFyYW0gdmFsdWUgLSBUaGUgdmFsdWUgdG8gd3JpdGUuXG4gICAgICogQHJldHVybnMgVGhpcy5cbiAgICAgKi9cbiAgICB3cml0ZUJvb2xlYW4odmFsdWUpIHtcbiAgICAgICAgdGhpcy53cml0ZVVpbnQ4KHZhbHVlID8gMHhmZiA6IDB4MDApO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogV3JpdGUgYHZhbHVlYCBhcyBhbiA4LWJpdCBzaWduZWQgaW50ZWdlciBhbmQgbW92ZSBwb2ludGVyIGZvcndhcmQgYnkgMSBieXRlLlxuICAgICAqIEBwYXJhbSB2YWx1ZSAtIFRoZSB2YWx1ZSB0byB3cml0ZS5cbiAgICAgKiBAcmV0dXJucyBUaGlzLlxuICAgICAqL1xuICAgIHdyaXRlSW50OCh2YWx1ZSkge1xuICAgICAgICB0aGlzLmVuc3VyZUF2YWlsYWJsZSgxKTtcbiAgICAgICAgdGhpcy5fZGF0YS5zZXRJbnQ4KHRoaXMub2Zmc2V0KyssIHZhbHVlKTtcbiAgICAgICAgdGhpcy5fdXBkYXRlTGFzdFdyaXR0ZW5CeXRlKCk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBgdmFsdWVgIGFzIGFuIDgtYml0IHVuc2lnbmVkIGludGVnZXIgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDFcbiAgICAgKiBieXRlLlxuICAgICAqIEBwYXJhbSB2YWx1ZSAtIFRoZSB2YWx1ZSB0byB3cml0ZS5cbiAgICAgKiBAcmV0dXJucyBUaGlzLlxuICAgICAqL1xuICAgIHdyaXRlVWludDgodmFsdWUpIHtcbiAgICAgICAgdGhpcy5lbnN1cmVBdmFpbGFibGUoMSk7XG4gICAgICAgIHRoaXMuX2RhdGEuc2V0VWludDgodGhpcy5vZmZzZXQrKywgdmFsdWUpO1xuICAgICAgICB0aGlzLl91cGRhdGVMYXN0V3JpdHRlbkJ5dGUoKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEFuIGFsaWFzIGZvciB7QGxpbmsgSU9CdWZmZXIjd3JpdGVVaW50OH0uXG4gICAgICogQHBhcmFtIHZhbHVlIC0gVGhlIHZhbHVlIHRvIHdyaXRlLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgd3JpdGVCeXRlKHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB0aGlzLndyaXRlVWludDgodmFsdWUpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhbGwgZWxlbWVudHMgb2YgYGJ5dGVzYCBhcyB1aW50OCB2YWx1ZXMgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5XG4gICAgICogYGJ5dGVzLmxlbmd0aGAgYnl0ZXMuXG4gICAgICogQHBhcmFtIGJ5dGVzIC0gVGhlIGFycmF5IG9mIGJ5dGVzIHRvIHdyaXRlLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgd3JpdGVCeXRlcyhieXRlcykge1xuICAgICAgICB0aGlzLmVuc3VyZUF2YWlsYWJsZShieXRlcy5sZW5ndGgpO1xuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L3ByZWZlci1mb3Itb2ZcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBieXRlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgdGhpcy5fZGF0YS5zZXRVaW50OCh0aGlzLm9mZnNldCsrLCBieXRlc1tpXSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5fdXBkYXRlTGFzdFdyaXR0ZW5CeXRlKCk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBgdmFsdWVgIGFzIGEgMTYtYml0IHNpZ25lZCBpbnRlZ2VyIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSAyXG4gICAgICogYnl0ZXMuXG4gICAgICogQHBhcmFtIHZhbHVlIC0gVGhlIHZhbHVlIHRvIHdyaXRlLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgd3JpdGVJbnQxNih2YWx1ZSkge1xuICAgICAgICB0aGlzLmVuc3VyZUF2YWlsYWJsZSgyKTtcbiAgICAgICAgdGhpcy5fZGF0YS5zZXRJbnQxNih0aGlzLm9mZnNldCwgdmFsdWUsIHRoaXMubGl0dGxlRW5kaWFuKTtcbiAgICAgICAgdGhpcy5vZmZzZXQgKz0gMjtcbiAgICAgICAgdGhpcy5fdXBkYXRlTGFzdFdyaXR0ZW5CeXRlKCk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBgdmFsdWVgIGFzIGEgMTYtYml0IHVuc2lnbmVkIGludGVnZXIgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDJcbiAgICAgKiBieXRlcy5cbiAgICAgKiBAcGFyYW0gdmFsdWUgLSBUaGUgdmFsdWUgdG8gd3JpdGUuXG4gICAgICogQHJldHVybnMgVGhpcy5cbiAgICAgKi9cbiAgICB3cml0ZVVpbnQxNih2YWx1ZSkge1xuICAgICAgICB0aGlzLmVuc3VyZUF2YWlsYWJsZSgyKTtcbiAgICAgICAgdGhpcy5fZGF0YS5zZXRVaW50MTYodGhpcy5vZmZzZXQsIHZhbHVlLCB0aGlzLmxpdHRsZUVuZGlhbik7XG4gICAgICAgIHRoaXMub2Zmc2V0ICs9IDI7XG4gICAgICAgIHRoaXMuX3VwZGF0ZUxhc3RXcml0dGVuQnl0ZSgpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogV3JpdGUgYHZhbHVlYCBhcyBhIDMyLWJpdCBzaWduZWQgaW50ZWdlciBhbmQgbW92ZSBwb2ludGVyIGZvcndhcmQgYnkgNFxuICAgICAqIGJ5dGVzLlxuICAgICAqIEBwYXJhbSB2YWx1ZSAtIFRoZSB2YWx1ZSB0byB3cml0ZS5cbiAgICAgKiBAcmV0dXJucyBUaGlzLlxuICAgICAqL1xuICAgIHdyaXRlSW50MzIodmFsdWUpIHtcbiAgICAgICAgdGhpcy5lbnN1cmVBdmFpbGFibGUoNCk7XG4gICAgICAgIHRoaXMuX2RhdGEuc2V0SW50MzIodGhpcy5vZmZzZXQsIHZhbHVlLCB0aGlzLmxpdHRsZUVuZGlhbik7XG4gICAgICAgIHRoaXMub2Zmc2V0ICs9IDQ7XG4gICAgICAgIHRoaXMuX3VwZGF0ZUxhc3RXcml0dGVuQnl0ZSgpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogV3JpdGUgYHZhbHVlYCBhcyBhIDMyLWJpdCB1bnNpZ25lZCBpbnRlZ2VyIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSA0XG4gICAgICogYnl0ZXMuXG4gICAgICogQHBhcmFtIHZhbHVlIC0gVGhlIHZhbHVlIHRvIHdyaXRlLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgd3JpdGVVaW50MzIodmFsdWUpIHtcbiAgICAgICAgdGhpcy5lbnN1cmVBdmFpbGFibGUoNCk7XG4gICAgICAgIHRoaXMuX2RhdGEuc2V0VWludDMyKHRoaXMub2Zmc2V0LCB2YWx1ZSwgdGhpcy5saXR0bGVFbmRpYW4pO1xuICAgICAgICB0aGlzLm9mZnNldCArPSA0O1xuICAgICAgICB0aGlzLl91cGRhdGVMYXN0V3JpdHRlbkJ5dGUoKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdyaXRlIGB2YWx1ZWAgYXMgYSAzMi1iaXQgZmxvYXRpbmcgbnVtYmVyIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSA0XG4gICAgICogYnl0ZXMuXG4gICAgICogQHBhcmFtIHZhbHVlIC0gVGhlIHZhbHVlIHRvIHdyaXRlLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgd3JpdGVGbG9hdDMyKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQXZhaWxhYmxlKDQpO1xuICAgICAgICB0aGlzLl9kYXRhLnNldEZsb2F0MzIodGhpcy5vZmZzZXQsIHZhbHVlLCB0aGlzLmxpdHRsZUVuZGlhbik7XG4gICAgICAgIHRoaXMub2Zmc2V0ICs9IDQ7XG4gICAgICAgIHRoaXMuX3VwZGF0ZUxhc3RXcml0dGVuQnl0ZSgpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogV3JpdGUgYHZhbHVlYCBhcyBhIDY0LWJpdCBmbG9hdGluZyBudW1iZXIgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDhcbiAgICAgKiBieXRlcy5cbiAgICAgKiBAcGFyYW0gdmFsdWUgLSBUaGUgdmFsdWUgdG8gd3JpdGUuXG4gICAgICogQHJldHVybnMgVGhpcy5cbiAgICAgKi9cbiAgICB3cml0ZUZsb2F0NjQodmFsdWUpIHtcbiAgICAgICAgdGhpcy5lbnN1cmVBdmFpbGFibGUoOCk7XG4gICAgICAgIHRoaXMuX2RhdGEuc2V0RmxvYXQ2NCh0aGlzLm9mZnNldCwgdmFsdWUsIHRoaXMubGl0dGxlRW5kaWFuKTtcbiAgICAgICAgdGhpcy5vZmZzZXQgKz0gODtcbiAgICAgICAgdGhpcy5fdXBkYXRlTGFzdFdyaXR0ZW5CeXRlKCk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBgdmFsdWVgIGFzIGEgNjQtYml0IHNpZ25lZCBiaWdpbnQgYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDhcbiAgICAgKiBieXRlcy5cbiAgICAgKiBAcGFyYW0gdmFsdWUgLSBUaGUgdmFsdWUgdG8gd3JpdGUuXG4gICAgICogQHJldHVybnMgVGhpcy5cbiAgICAgKi9cbiAgICB3cml0ZUJpZ0ludDY0KHZhbHVlKSB7XG4gICAgICAgIHRoaXMuZW5zdXJlQXZhaWxhYmxlKDgpO1xuICAgICAgICB0aGlzLl9kYXRhLnNldEJpZ0ludDY0KHRoaXMub2Zmc2V0LCB2YWx1ZSwgdGhpcy5saXR0bGVFbmRpYW4pO1xuICAgICAgICB0aGlzLm9mZnNldCArPSA4O1xuICAgICAgICB0aGlzLl91cGRhdGVMYXN0V3JpdHRlbkJ5dGUoKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdyaXRlIGB2YWx1ZWAgYXMgYSA2NC1iaXQgdW5zaWduZWQgYmlnaW50IGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSA4XG4gICAgICogYnl0ZXMuXG4gICAgICogQHBhcmFtIHZhbHVlIC0gVGhlIHZhbHVlIHRvIHdyaXRlLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgd3JpdGVCaWdVaW50NjQodmFsdWUpIHtcbiAgICAgICAgdGhpcy5lbnN1cmVBdmFpbGFibGUoOCk7XG4gICAgICAgIHRoaXMuX2RhdGEuc2V0QmlnVWludDY0KHRoaXMub2Zmc2V0LCB2YWx1ZSwgdGhpcy5saXR0bGVFbmRpYW4pO1xuICAgICAgICB0aGlzLm9mZnNldCArPSA4O1xuICAgICAgICB0aGlzLl91cGRhdGVMYXN0V3JpdHRlbkJ5dGUoKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdyaXRlIHRoZSBjaGFyQ29kZSBvZiBgc3RyYCdzIGZpcnN0IGNoYXJhY3RlciBhcyBhbiA4LWJpdCB1bnNpZ25lZCBpbnRlZ2VyXG4gICAgICogYW5kIG1vdmUgcG9pbnRlciBmb3J3YXJkIGJ5IDEgYnl0ZS5cbiAgICAgKiBAcGFyYW0gc3RyIC0gVGhlIGNoYXJhY3RlciB0byB3cml0ZS5cbiAgICAgKiBAcmV0dXJucyBUaGlzLlxuICAgICAqL1xuICAgIHdyaXRlQ2hhcihzdHIpIHtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHVuaWNvcm4vcHJlZmVyLWNvZGUtcG9pbnRcbiAgICAgICAgcmV0dXJuIHRoaXMud3JpdGVVaW50OChzdHIuY2hhckNvZGVBdCgwKSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdyaXRlIHRoZSBjaGFyQ29kZXMgb2YgYWxsIGBzdHJgJ3MgY2hhcmFjdGVycyBhcyA4LWJpdCB1bnNpZ25lZCBpbnRlZ2Vyc1xuICAgICAqIGFuZCBtb3ZlIHBvaW50ZXIgZm9yd2FyZCBieSBgc3RyLmxlbmd0aGAgYnl0ZXMuXG4gICAgICogQHBhcmFtIHN0ciAtIFRoZSBjaGFyYWN0ZXJzIHRvIHdyaXRlLlxuICAgICAqIEByZXR1cm5zIFRoaXMuXG4gICAgICovXG4gICAgd3JpdGVDaGFycyhzdHIpIHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdHIubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSB1bmljb3JuL3ByZWZlci1jb2RlLXBvaW50XG4gICAgICAgICAgICB0aGlzLndyaXRlVWludDgoc3RyLmNoYXJDb2RlQXQoaSkpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBVVEYtOCBlbmNvZGUgYW5kIHdyaXRlIGBzdHJgIHRvIHRoZSBjdXJyZW50IHBvaW50ZXIgb2Zmc2V0IGFuZCBtb3ZlIHBvaW50ZXJcbiAgICAgKiBmb3J3YXJkIGFjY29yZGluZyB0byB0aGUgZW5jb2RlZCBsZW5ndGguXG4gICAgICogQHBhcmFtIHN0ciAtIFRoZSBzdHJpbmcgdG8gd3JpdGUuXG4gICAgICogQHJldHVybnMgVGhpcy5cbiAgICAgKi9cbiAgICB3cml0ZVV0Zjgoc3RyKSB7XG4gICAgICAgIHJldHVybiB0aGlzLndyaXRlQnl0ZXMoZW5jb2RlKHN0cikpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBFeHBvcnQgYSBVaW50OEFycmF5IHZpZXcgb2YgdGhlIGludGVybmFsIGJ1ZmZlci5cbiAgICAgKiBUaGUgdmlldyBzdGFydHMgYXQgdGhlIGJ5dGUgb2Zmc2V0IGFuZCBpdHMgbGVuZ3RoXG4gICAgICogaXMgY2FsY3VsYXRlZCB0byBzdG9wIGF0IHRoZSBsYXN0IHdyaXR0ZW4gYnl0ZSBvciB0aGUgb3JpZ2luYWwgbGVuZ3RoLlxuICAgICAqIEByZXR1cm5zIEEgbmV3IFVpbnQ4QXJyYXkgdmlldy5cbiAgICAgKi9cbiAgICB0b0FycmF5KCkge1xuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkodGhpcy5idWZmZXIsIHRoaXMuYnl0ZU9mZnNldCwgdGhpcy5sYXN0V3JpdHRlbkJ5dGUpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiAgR2V0IHRoZSB0b3RhbCBudW1iZXIgb2YgYnl0ZXMgd3JpdHRlbiBzbyBmYXIsIHJlZ2FyZGxlc3Mgb2YgdGhlIGN1cnJlbnQgb2Zmc2V0LlxuICAgICAqIEByZXR1cm5zIC0gVG90YWwgbnVtYmVyIG9mIGJ5dGVzLlxuICAgICAqL1xuICAgIGdldFdyaXR0ZW5CeXRlTGVuZ3RoKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5sYXN0V3JpdHRlbkJ5dGUgLSB0aGlzLmJ5dGVPZmZzZXQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSB0aGUgbGFzdCB3cml0dGVuIGJ5dGUgb2Zmc2V0XG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBfdXBkYXRlTGFzdFdyaXR0ZW5CeXRlKCkge1xuICAgICAgICBpZiAodGhpcy5vZmZzZXQgPiB0aGlzLmxhc3RXcml0dGVuQnl0ZSkge1xuICAgICAgICAgICAgdGhpcy5sYXN0V3JpdHRlbkJ5dGUgPSB0aGlzLm9mZnNldDtcbiAgICAgICAgfVxuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUlPQnVmZmVyLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/iobuffer/lib-esm/IOBuffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/iobuffer/lib-esm/text.js":
/*!***********************************************!*\
  !*** ./node_modules/iobuffer/lib-esm/text.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\nfunction decode(bytes, encoding = 'utf8') {\n    const decoder = new TextDecoder(encoding);\n    return decoder.decode(bytes);\n}\nconst encoder = new TextEncoder();\nfunction encode(str) {\n    return encoder.encode(str);\n}\n//# sourceMappingURL=text.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaW9idWZmZXIvbGliLWVzbS90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2tkb2ktbWFya2V0cGxhY2UvLi9ub2RlX21vZHVsZXMvaW9idWZmZXIvbGliLWVzbS90ZXh0LmpzP2RlYzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGRlY29kZShieXRlcywgZW5jb2RpbmcgPSAndXRmOCcpIHtcbiAgICBjb25zdCBkZWNvZGVyID0gbmV3IFRleHREZWNvZGVyKGVuY29kaW5nKTtcbiAgICByZXR1cm4gZGVjb2Rlci5kZWNvZGUoYnl0ZXMpO1xufVxuY29uc3QgZW5jb2RlciA9IG5ldyBUZXh0RW5jb2RlcigpO1xuZXhwb3J0IGZ1bmN0aW9uIGVuY29kZShzdHIpIHtcbiAgICByZXR1cm4gZW5jb2Rlci5lbmNvZGUoc3RyKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRleHQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/iobuffer/lib-esm/text.js\n");

/***/ })

};
;